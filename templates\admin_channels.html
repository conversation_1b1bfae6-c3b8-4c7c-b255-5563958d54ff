<!DOCTYPE html>
<html lang="pl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>📺 <PERSON>ar<PERSON><PERSON><PERSON><PERSON><PERSON> Discord - Admin Panel</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <style>
      :root {
        --admin-primary: #2c3e50;
        --admin-secondary: #34495e;
        --admin-accent: #3498db;
        --admin-success: #27ae60;
        --admin-warning: #f39c12;
        --admin-danger: #e74c3c;
        --admin-dark: #1a252f;
        --discord-color: #5865f2;
      }

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .admin-navbar {
        background: var(--admin-dark) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .admin-content {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        margin: 20px;
        padding: 30px;
      }

      .channel-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: none;
        margin-bottom: 20px;
        transition: transform 0.3s ease;
      }

      .channel-card:hover {
        transform: translateY(-5px);
      }

      .channel-card .card-header {
        background: linear-gradient(
          135deg,
          var(--discord-color) 0%,
          #4752c4 100%
        );
        color: white;
        border-radius: 15px 15px 0 0 !important;
        border: none;
        padding: 20px;
      }

      .btn-admin {
        background: linear-gradient(
          135deg,
          var(--admin-accent) 0%,
          #5dade2 100%
        );
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .btn-admin:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        color: white;
      }

      .btn-discord {
        background: linear-gradient(
          135deg,
          var(--discord-color) 0%,
          #4752c4 100%
        );
      }

      .btn-admin-success {
        background: linear-gradient(
          135deg,
          var(--admin-success) 0%,
          #58d68d 100%
        );
      }

      .btn-admin-danger {
        background: linear-gradient(
          135deg,
          var(--admin-danger) 0%,
          #ec7063 100%
        );
      }

      .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .status-active {
        background: var(--admin-success);
        color: white;
      }

      .status-inactive {
        background: var(--admin-danger);
        color: white;
      }

      .strategy-badge {
        background: var(--admin-accent);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
      }

      .bot-list {
        max-height: 150px;
        overflow-y: auto;
      }

      .bot-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 8px 12px;
        margin-bottom: 5px;
        display: flex;
        justify-content: between;
        align-items: center;
      }

      .modal-content {
        border-radius: 15px;
        border: none;
      }

      .modal-header {
        background: linear-gradient(
          135deg,
          var(--discord-color) 0%,
          #4752c4 100%
        );
        color: white;
        border-radius: 15px 15px 0 0;
      }

      .form-control,
      .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        transition: border-color 0.3s ease;
      }

      .form-control:focus,
      .form-select:focus {
        border-color: var(--discord-color);
        box-shadow: 0 0 0 0.2rem rgba(88, 101, 242, 0.25);
      }

      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .fade-in {
        animation: fadeIn 0.5s ease-in;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
      <div class="container-fluid">
        <a class="navbar-brand text-white" href="/">
          <i class="fab fa-discord"></i>
          Zarządzanie Kanałami Discord
        </a>
        <div class="navbar-nav ms-auto">
          <a class="nav-link text-light" href="/">
            <i class="fas fa-arrow-left"></i>
            Powrót do Dashboard
          </a>
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="admin-content fade-in">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h2 mb-0">
            <i class="fab fa-discord text-primary"></i>
            Zarządzanie Kanałami Discord
          </h1>
          <div class="d-flex gap-2">
            <button class="btn btn-admin" onclick="loadChannels()">
              <i class="fas fa-sync-alt"></i>
              Odśwież
            </button>
            <button
              class="btn btn-discord"
              data-bs-toggle="modal"
              data-bs-target="#channelModal"
              onclick="openCreateModal()"
            >
              <i class="fas fa-plus"></i>
              Dodaj kanał
            </button>
          </div>
        </div>

        <!-- Info Alert -->
        <div class="alert alert-info" role="alert">
          <i class="fas fa-info-circle"></i>
          <strong>Zasada:</strong> Jeden kanał Discord = jedna strategia
          tradingowa. Każdy kanał może mieć własną whitelistę botów.
        </div>

        <!-- Channels List -->
        <div class="row" id="channelsContainer">
          <div class="col-12 text-center">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Ładowanie...</span>
            </div>
            <p class="mt-2">Ładowanie kanałów...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Channel Modal -->
    <div class="modal fade" id="channelModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalTitle">
              <i class="fab fa-discord"></i>
              Dodaj kanał Discord
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <form id="channelForm">
              <input type="hidden" id="channelId" />

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="channelDiscordId" class="form-label"
                      >Discord Channel ID *</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="channelDiscordId"
                      required
                    />
                    <div class="form-text">
                      <i class="fas fa-info-circle"></i>
                      Kliknij prawym przyciskiem na kanał → Kopiuj ID
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="channelName" class="form-label"
                      >Nazwa kanału *</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="channelName"
                      required
                    />
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="guildId" class="form-label"
                      >Guild ID (opcjonalne)</label
                    >
                    <input type="text" class="form-control" id="guildId" />
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="guildName" class="form-label"
                      >Nazwa serwera (opcjonalne)</label
                    >
                    <input type="text" class="form-control" id="guildName" />
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="strategyId" class="form-label"
                      >Strategia *</label
                    >
                    <select class="form-select" id="strategyId" required>
                      <option value="">Wybierz strategię...</option>
                    </select>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="isActive" class="form-label">Status</label>
                    <select class="form-select" id="isActive">
                      <option value="true">Aktywny</option>
                      <option value="false">Nieaktywny</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="allowBotMessages"
                  />
                  <label class="form-check-label" for="allowBotMessages">
                    Zezwalaj na wiadomości od botów
                  </label>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Anuluj
            </button>
            <button
              type="button"
              class="btn btn-discord"
              onclick="saveChannel()"
            >
              <span id="saveButtonText">Zapisz kanał</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bot Modal -->
    <div class="modal fade" id="botModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-robot"></i>
              Dodaj bota do whitelisty
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <form id="botForm">
              <input type="hidden" id="botChannelId" />

              <div class="mb-3">
                <label for="botId" class="form-label">Bot ID *</label>
                <input type="text" class="form-control" id="botId" required />
                <div class="form-text">
                  <i class="fas fa-info-circle"></i>
                  Kliknij prawym przyciskiem na bota → Kopiuj ID
                </div>
              </div>

              <div class="mb-3">
                <label for="botName" class="form-label"
                  >Nazwa bota (opcjonalne)</label
                >
                <input type="text" class="form-control" id="botName" />
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Anuluj
            </button>
            <button
              type="button"
              class="btn btn-admin-success"
              onclick="saveBot()"
            >
              <i class="fas fa-plus"></i>
              Dodaj bota
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title">
              <i class="fas fa-exclamation-triangle"></i>
              Potwierdź usunięcie
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <p>
              Czy na pewno chcesz usunąć <span id="deleteItemType"></span>
              <strong id="deleteItemName"></strong>?
            </p>
            <p class="text-danger">
              <i class="fas fa-warning"></i>
              Ta akcja jest nieodwracalna!
            </p>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Anuluj
            </button>
            <button
              type="button"
              class="btn btn-admin-danger"
              onclick="confirmDelete()"
            >
              <i class="fas fa-trash"></i>
              Usuń
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      let channels = [];
      let strategies = [];
      let currentChannelId = null;
      let deleteItemId = null;
      let deleteItemType = null;

      // Inicjalizacja
      document.addEventListener("DOMContentLoaded", function () {
        loadChannels();
        loadStrategies();
      });

      // Ładowanie kanałów
      async function loadChannels() {
        try {
          const response = await fetch("/api/channels");
          if (!response.ok) throw new Error("Błąd ładowania kanałów");

          channels = await response.json();
          renderChannels();
        } catch (error) {
          console.error("Error loading channels:", error);
          showError("Błąd ładowania kanałów: " + error.message);
        }
      }

      // Ładowanie strategii
      async function loadStrategies() {
        try {
          const response = await fetch("/api/strategies");
          if (!response.ok) throw new Error("Błąd ładowania strategii");

          strategies = await response.json();
          populateStrategySelect();
        } catch (error) {
          console.error("Error loading strategies:", error);
          showError("Błąd ładowania strategii: " + error.message);
        }
      }

      // Wypełnij select strategii
      function populateStrategySelect() {
        const select = document.getElementById("strategyId");
        select.innerHTML = '<option value="">Wybierz strategię...</option>';

        strategies
          .filter((s) => s.is_active)
          .forEach((strategy) => {
            const option = document.createElement("option");
            option.value = strategy.id;
            option.textContent = strategy.name;
            select.appendChild(option);
          });
      }

      // Renderowanie kanałów
      function renderChannels() {
        const container = document.getElementById("channelsContainer");

        if (channels.length === 0) {
          container.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="channel-card">
                            <div class="card-body py-5">
                                <i class="fab fa-discord fa-3x text-muted mb-3"></i>
                                <h5>Brak kanałów Discord</h5>
                                <p class="text-muted">Dodaj pierwszy kanał, aby rozpocząć monitorowanie sygnałów.</p>
                                <button class="btn btn-discord" data-bs-toggle="modal" data-bs-target="#channelModal" onclick="openCreateModal()">
                                    <i class="fas fa-plus"></i>
                                    Dodaj kanał
                                </button>
                            </div>
                        </div>
                    </div>
                `;
          return;
        }

        container.innerHTML = channels
          .map(
            (channel) => `
                <div class="col-md-6 col-lg-4">
                    <div class="channel-card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fab fa-discord"></i>
                                    ${channel.channel_name}
                                </h6>
                                <span class="status-badge ${
                                  channel.is_active
                                    ? "status-active"
                                    : "status-inactive"
                                }">
                                    ${
                                      channel.is_active
                                        ? "Aktywny"
                                        : "Nieaktywny"
                                    }
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Channel ID:</small>
                                <div class="font-monospace small">${
                                  channel.channel_id
                                }</div>
                            </div>

                            ${
                              channel.strategy_name
                                ? `
                                <div class="mb-3">
                                    <small class="text-muted">Strategia:</small>
                                    <div>
                                        <span class="strategy-badge">${channel.strategy_name}</span>
                                    </div>
                                </div>
                            `
                                : '<div class="mb-3"><span class="badge bg-warning">Brak strategii</span></div>'
                            }

                            <div class="mb-3">
                                <small class="text-muted">Boty (${
                                  channel.allow_bot_messages
                                    ? "włączone"
                                    : "wyłączone"
                                }):</small>
                                <div class="bot-list">
                                    ${
                                      channel.bot_whitelist &&
                                      channel.bot_whitelist.length > 0
                                        ? channel.bot_whitelist
                                            .map(
                                              (bot) => `
                                            <div class="bot-item">
                                                <span>${
                                                  bot.bot_name || bot.bot_id
                                                }</span>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteBot(${
                                                  bot.id
                                                }, '${
                                                bot.bot_name || bot.bot_id
                                              }')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        `
                                            )
                                            .join("")
                                        : '<small class="text-muted">Brak botów na whiteliście</small>'
                                    }
                                </div>
                                ${
                                  channel.allow_bot_messages
                                    ? `
                                    <button class="btn btn-sm btn-admin-success mt-2" onclick="addBot(${channel.id})">
                                        <i class="fas fa-plus"></i>
                                        Dodaj bota
                                    </button>
                                `
                                    : ""
                                }
                            </div>

                            <div class="d-flex gap-2">
                                <button class="btn btn-admin btn-sm flex-fill" onclick="editChannel(${
                                  channel.id
                                })">
                                    <i class="fas fa-edit"></i>
                                    Edytuj
                                </button>
                                <button class="btn btn-channels btn-sm" onclick="testChannelConnection(${
                                  channel.id
                                })" title="Testuj połączenie z kanałem">
                                    <i class="fas fa-plug"></i>
                                    Test
                                </button>
                                <button class="btn btn-admin-danger btn-sm" onclick="deleteChannel(${
                                  channel.id
                                }, '${channel.channel_name}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `
          )
          .join("");
      }

      // Otwórz modal tworzenia
      function openCreateModal() {
        currentChannelId = null;
        document.getElementById("modalTitle").innerHTML =
          '<i class="fab fa-discord"></i> Dodaj kanał Discord';
        document.getElementById("channelForm").reset();
        document.getElementById("channelId").value = "";
        document.getElementById("saveButtonText").textContent = "Dodaj kanał";
      }

      // Edytuj kanał
      async function editChannel(id) {
        try {
          const response = await fetch(`/api/channels/${id}`);
          if (!response.ok) throw new Error("Błąd ładowania kanału");

          const channel = await response.json();

          currentChannelId = id;
          document.getElementById("modalTitle").innerHTML =
            '<i class="fas fa-edit"></i> Edytuj kanał';
          document.getElementById("channelId").value = id;
          document.getElementById("channelDiscordId").value =
            channel.channel_id;
          document.getElementById("channelName").value = channel.channel_name;
          document.getElementById("guildId").value = channel.guild_id || "";
          document.getElementById("guildName").value = channel.guild_name || "";
          document.getElementById("strategyId").value =
            channel.strategy_id || "";
          document.getElementById("isActive").value =
            channel.is_active.toString();
          document.getElementById("allowBotMessages").checked =
            channel.allow_bot_messages;
          document.getElementById("saveButtonText").textContent =
            "Zaktualizuj kanał";

          new bootstrap.Modal(document.getElementById("channelModal")).show();
        } catch (error) {
          console.error("Error loading channel:", error);
          showError("Błąd ładowania kanału: " + error.message);
        }
      }

      // Zapisz kanał
      async function saveChannel() {
        const form = document.getElementById("channelForm");
        if (!form.checkValidity()) {
          form.reportValidity();
          return;
        }

        const saveButton = document.querySelector("#channelModal .btn-discord");
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML =
          '<span class="loading-spinner"></span> Zapisywanie...';
        saveButton.disabled = true;

        try {
          const channelData = {
            channel_id: document.getElementById("channelDiscordId").value,
            channel_name: document.getElementById("channelName").value,
            guild_id: document.getElementById("guildId").value || null,
            guild_name: document.getElementById("guildName").value || null,
            strategy_id:
              parseInt(document.getElementById("strategyId").value) || null,
            is_active: document.getElementById("isActive").value === "true",
            allow_bot_messages:
              document.getElementById("allowBotMessages").checked,
          };

          const url = currentChannelId
            ? `/api/channels/${currentChannelId}`
            : "/api/channels";
          const method = currentChannelId ? "PUT" : "POST";

          const response = await fetch(url, {
            method: method,
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(channelData),
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || "Błąd zapisywania kanału");
          }

          bootstrap.Modal.getInstance(
            document.getElementById("channelModal")
          ).hide();
          showSuccess(
            currentChannelId
              ? "Kanał zaktualizowany pomyślnie"
              : "Kanał dodany pomyślnie"
          );
          loadChannels();
        } catch (error) {
          console.error("Error saving channel:", error);
          showError("Błąd zapisywania kanału: " + error.message);
        } finally {
          saveButton.innerHTML = originalText;
          saveButton.disabled = false;
        }
      }

      // Dodaj bota
      function addBot(channelId) {
        document.getElementById("botChannelId").value = channelId;
        document.getElementById("botForm").reset();
        new bootstrap.Modal(document.getElementById("botModal")).show();
      }

      // Zapisz bota
      async function saveBot() {
        const form = document.getElementById("botForm");
        if (!form.checkValidity()) {
          form.reportValidity();
          return;
        }

        try {
          const channelId = document.getElementById("botChannelId").value;
          const botData = {
            bot_id: document.getElementById("botId").value,
            bot_name: document.getElementById("botName").value || null,
          };

          const response = await fetch(`/api/channels/${channelId}/bots`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(botData),
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || "Błąd dodawania bota");
          }

          bootstrap.Modal.getInstance(
            document.getElementById("botModal")
          ).hide();
          showSuccess("Bot dodany do whitelisty");
          loadChannels();
        } catch (error) {
          console.error("Error saving bot:", error);
          showError("Błąd dodawania bota: " + error.message);
        }
      }

      // Usuń kanał
      function deleteChannel(id, name) {
        deleteItemId = id;
        deleteItemType = "channel";
        document.getElementById("deleteItemType").textContent = "kanał";
        document.getElementById("deleteItemName").textContent = name;
        new bootstrap.Modal(document.getElementById("deleteModal")).show();
      }

      // Usuń bota
      function deleteBot(id, name) {
        deleteItemId = id;
        deleteItemType = "bot";
        document.getElementById("deleteItemType").textContent = "bota";
        document.getElementById("deleteItemName").textContent = name;
        new bootstrap.Modal(document.getElementById("deleteModal")).show();
      }

      // Potwierdź usunięcie
      async function confirmDelete() {
        try {
          let url;
          if (deleteItemType === "channel") {
            url = `/api/channels/${deleteItemId}`;
          } else if (deleteItemType === "bot") {
            url = `/api/bots/${deleteItemId}`;
          }

          const response = await fetch(url, {
            method: "DELETE",
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(
              error.error ||
                `Błąd usuwania ${
                  deleteItemType === "channel" ? "kanału" : "bota"
                }`
            );
          }

          bootstrap.Modal.getInstance(
            document.getElementById("deleteModal")
          ).hide();
          showSuccess(
            `${
              deleteItemType === "channel" ? "Kanał" : "Bot"
            } usunięty pomyślnie`
          );
          loadChannels();
        } catch (error) {
          console.error("Error deleting item:", error);
          showError(`Błąd usuwania: ${error.message}`);
        }
      }

      // Testuj połączenie z kanałem
      async function testChannelConnection(channelId) {
        const testButton = document.querySelector(
          `button[onclick="testChannelConnection(${channelId})"]`
        );
        const originalText = testButton.innerHTML;

        testButton.innerHTML =
          '<span class="loading-spinner"></span> Testowanie...';
        testButton.disabled = true;

        try {
          const response = await fetch(`/api/channels/${channelId}/test`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
          });

          const result = await response.json();

          // Pokaż wyniki w modalu
          showTestResults(result);
        } catch (error) {
          console.error("Error testing channel connection:", error);
          showError("Błąd testowania połączenia: " + error.message);
        } finally {
          testButton.innerHTML = originalText;
          testButton.disabled = false;
        }
      }

      // Pokaż wyniki testowania
      function showTestResults(result) {
        // Utwórz modal dynamicznie jeśli nie istnieje
        let modal = document.getElementById("testResultModal");
        if (!modal) {
          const modalHtml = `
            <div class="modal fade" id="testResultModal" tabindex="-1">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="testResultTitle">Test połączenia</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                  </div>
                  <div class="modal-body">
                    <div id="testResultContent"></div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zamknij</button>
                  </div>
                </div>
              </div>
            </div>
          `;
          document.body.insertAdjacentHTML("beforeend", modalHtml);
          modal = document.getElementById("testResultModal");
        }

        const title = document.getElementById("testResultTitle");
        const content = document.getElementById("testResultContent");

        // Ustaw tytuł
        if (result.success) {
          title.innerHTML =
            '<i class="fas fa-check-circle text-success"></i> Test połączenia - SUKCES';
          title.className = "modal-title text-success";
        } else {
          title.innerHTML =
            '<i class="fas fa-times-circle text-danger"></i> Test połączenia - BŁĄD';
          title.className = "modal-title text-danger";
        }

        // Generuj zawartość
        let html = `
          <style>
            .test-section { margin-bottom: 20px; }
            .test-item {
              padding: 8px 12px;
              margin: 5px 0;
              border-radius: 5px;
              display: flex;
              align-items: center;
              gap: 8px;
            }
            .test-item.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .test-item.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .test-item.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
            .test-details {
              font-size: 0.9rem;
              color: #666;
              margin-top: 8px;
              padding: 8px;
              background: #f8f9fa;
              border-radius: 4px;
            }
          </style>
        `;

        // Status połączenia
        html += '<div class="test-section">';
        html += '<h6><i class="fas fa-robot"></i> Status Bota</h6>';
        html += `<div class="test-item ${
          result.bot_connected ? "success" : "error"
        }">`;
        html += `<i class="fas fa-${
          result.bot_connected ? "check" : "times"
        }"></i>`;
        html += `Połączenie z Discord: ${
          result.bot_connected ? "Połączony" : "Błąd połączenia"
        }`;
        html += "</div>";

        if (result.bot_info) {
          html += '<div class="test-details">';
          html += `<strong>Bot:</strong> ${result.bot_info.name}#${result.bot_info.discriminator}<br>`;
          html += `<strong>ID:</strong> ${result.bot_info.id}`;
          html += "</div>";
        }
        html += "</div>";

        // Status kanału
        html += '<div class="test-section">';
        html += '<h6><i class="fas fa-hashtag"></i> Status Kanału</h6>';
        html += `<div class="test-item ${
          result.channel_accessible ? "success" : "error"
        }">`;
        html += `<i class="fas fa-${
          result.channel_accessible ? "check" : "times"
        }"></i>`;
        html += `Dostęp do kanału: ${
          result.channel_accessible ? "Dostępny" : "Niedostępny"
        }`;
        html += "</div>";

        if (result.channel_info) {
          html += '<div class="test-details">';
          html += `<strong>Kanał:</strong> #${result.channel_info.name}<br>`;
          html += `<strong>ID:</strong> ${result.channel_info.id}<br>`;
          html += `<strong>Typ:</strong> ${result.channel_info.type}`;
          html += "</div>";
        }
        html += "</div>";

        // Status serwera
        if (result.guild_info) {
          html += '<div class="test-section">';
          html += '<h6><i class="fas fa-server"></i> Status Serwera</h6>';
          html += '<div class="test-item success">';
          html += '<i class="fas fa-check"></i>';
          html += "Dostęp do serwera: Dostępny";
          html += "</div>";
          html += '<div class="test-details">';
          html += `<strong>Serwer:</strong> ${result.guild_info.name}<br>`;
          html += `<strong>ID:</strong> ${result.guild_info.id}<br>`;
          html += `<strong>Członkowie:</strong> ${result.guild_info.member_count}`;
          html += "</div>";
          html += "</div>";
        }

        // Uprawnienia
        html += '<div class="test-section">';
        html += '<h6><i class="fas fa-key"></i> Uprawnienia</h6>';
        html += `<div class="test-item ${
          result.can_read_messages ? "success" : "warning"
        }">`;
        html += `<i class="fas fa-${
          result.can_read_messages ? "check" : "exclamation-triangle"
        }"></i>`;
        html += `Odczyt wiadomości: ${
          result.can_read_messages ? "Dozwolone" : "Brak uprawnień"
        }`;
        html += "</div>";
        html += `<div class="test-item ${
          result.can_send_messages ? "success" : "warning"
        }">`;
        html += `<i class="fas fa-${
          result.can_send_messages ? "check" : "exclamation-triangle"
        }"></i>`;
        html += `Wysyłanie wiadomości: ${
          result.can_send_messages ? "Dozwolone" : "Brak uprawnień"
        }`;
        html += "</div>";
        html += "</div>";

        // Ostrzeżenia
        if (result.warnings && result.warnings.length > 0) {
          html += '<div class="test-section">';
          html +=
            '<h6><i class="fas fa-exclamation-triangle text-warning"></i> Ostrzeżenia</h6>';
          result.warnings.forEach((warning) => {
            html += `<div class="test-item warning">`;
            html += '<i class="fas fa-exclamation-triangle"></i>';
            html += warning;
            html += "</div>";
          });
          html += "</div>";
        }

        // Błędy
        if (result.error) {
          html += '<div class="test-section">';
          html +=
            '<h6><i class="fas fa-times-circle text-danger"></i> Błąd</h6>';
          html += `<div class="test-item error">`;
          html += '<i class="fas fa-times"></i>';
          html += result.error;
          html += "</div>";
          if (result.details) {
            html += `<div class="test-details">${result.details}</div>`;
          }
          html += "</div>";
        }

        content.innerHTML = html;
        new bootstrap.Modal(modal).show();
      }

      // Funkcje pomocnicze
      function showSuccess(message) {
        console.log("Success:", message);
      }

      function showError(message) {
        console.error("Error:", message);
        alert(message); // Tymczasowe rozwiązanie
      }
    </script>
  </body>
</html>
