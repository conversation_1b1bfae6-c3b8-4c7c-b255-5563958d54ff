<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ Konfiguracja Systemu - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-accent: #3498db;
            --admin-success: #27ae60;
            --admin-warning: #f39c12;
            --admin-danger: #e74c3c;
            --admin-dark: #1a252f;
            --config-color: #8e44ad;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-navbar {
            background: var(--admin-dark) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .admin-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
        }

        .config-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .config-card:hover {
            transform: translateY(-5px);
        }

        .config-card .card-header {
            background: linear-gradient(135deg, var(--config-color) 0%, #a569bd 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--admin-accent) 0%, #5dade2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            color: white;
        }

        .btn-config {
            background: linear-gradient(135deg, var(--config-color) 0%, #a569bd 100%);
        }

        .btn-admin-success {
            background: linear-gradient(135deg, var(--admin-success) 0%, #58d68d 100%);
        }

        .btn-admin-danger {
            background: linear-gradient(135deg, var(--admin-danger) 0%, #ec7063 100%);
        }

        .config-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid var(--config-color);
        }

        .config-key {
            font-weight: 600;
            color: var(--admin-primary);
            font-family: 'Courier New', monospace;
        }

        .config-value {
            color: #666;
            margin-top: 5px;
        }

        .config-description {
            font-size: 0.9rem;
            color: #888;
            margin-top: 5px;
        }

        .config-type-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .type-string {
            background: #e3f2fd;
            color: #1976d2;
        }

        .type-integer {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .type-float {
            background: #e8f5e8;
            color: #388e3c;
        }

        .type-boolean {
            background: #fff3e0;
            color: #f57c00;
        }

        .sensitive-badge {
            background: var(--admin-danger);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: 8px;
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--config-color) 0%, #a569bd 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--config-color);
            box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .config-categories {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .category-filter {
            padding: 8px 16px;
            border: 2px solid var(--config-color);
            background: white;
            color: var(--config-color);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .category-filter.active {
            background: var(--config-color);
            color: white;
        }

        .category-filter:hover {
            background: var(--config-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="fas fa-cog"></i>
                Konfiguracja Systemu
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-light" href="/">
                    <i class="fas fa-arrow-left"></i>
                    Powrót do Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="admin-content fade-in">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0">
                    <i class="fas fa-cog text-primary"></i>
                    Konfiguracja Systemu
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-admin" onclick="loadConfigs()">
                        <i class="fas fa-sync-alt"></i>
                        Odśwież
                    </button>
                    <button class="btn btn-config" data-bs-toggle="modal" data-bs-target="#configModal" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i>
                        Dodaj konfigurację
                    </button>
                </div>
            </div>

            <!-- Info Alert -->
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle"></i>
                <strong>Konfiguracja systemowa:</strong> Zarządzaj parametrami globalnymi, ustawieniami API i konfiguracją aplikacji.
                Wrażliwe dane (API keys) są automatycznie maskowane.
            </div>

            <!-- Category Filters -->
            <div class="config-categories">
                <div class="category-filter active" data-category="all">
                    <i class="fas fa-list"></i>
                    Wszystkie
                </div>
                <div class="category-filter" data-category="api">
                    <i class="fas fa-key"></i>
                    API
                </div>
                <div class="category-filter" data-category="system">
                    <i class="fas fa-server"></i>
                    System
                </div>
                <div class="category-filter" data-category="notifications">
                    <i class="fas fa-bell"></i>
                    Powiadomienia
                </div>
                <div class="category-filter" data-category="performance">
                    <i class="fas fa-tachometer-alt"></i>
                    Wydajność
                </div>
            </div>

            <!-- Configurations List -->
            <div class="row" id="configsContainer">
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Ładowanie...</span>
                    </div>
                    <p class="mt-2">Ładowanie konfiguracji...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Config Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-cog"></i>
                        Dodaj konfigurację
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <input type="hidden" id="configId">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="configKey" class="form-label">Klucz konfiguracji *</label>
                                    <input type="text" class="form-control" id="configKey" required>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        Używaj snake_case, np. bybit_api_key
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="configType" class="form-label">Typ *</label>
                                    <select class="form-select" id="configType" required>
                                        <option value="string">String</option>
                                        <option value="integer">Integer</option>
                                        <option value="float">Float</option>
                                        <option value="boolean">Boolean</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="configValue" class="form-label">Wartość</label>
                            <input type="text" class="form-control" id="configValue">
                            <div class="form-text" id="valueHelp">
                                Wprowadź wartość zgodną z wybranym typem
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="configDescription" class="form-label">Opis</label>
                            <textarea class="form-control" id="configDescription" rows="2"></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isSensitive">
                                <label class="form-check-label" for="isSensitive">
                                    <i class="fas fa-shield-alt text-danger"></i>
                                    Dane wrażliwe (API keys, hasła)
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="button" class="btn btn-config" onclick="saveConfig()">
                        <span id="saveButtonText">Zapisz konfigurację</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Potwierdź usunięcie
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Czy na pewno chcesz usunąć konfigurację <strong id="deleteConfigKey"></strong>?</p>
                    <p class="text-danger">
                        <i class="fas fa-warning"></i>
                        Ta akcja jest nieodwracalna!
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="button" class="btn btn-admin-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i>
                        Usuń konfigurację
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let configs = [];
        let currentConfigId = null;
        let deleteConfigId = null;
        let currentCategory = 'all';

        // Inicjalizacja
        document.addEventListener('DOMContentLoaded', function() {
            loadConfigs();
            setupCategoryFilters();
            setupTypeHelp();
        });

        // Konfiguracja filtrów kategorii
        function setupCategoryFilters() {
            document.querySelectorAll('.category-filter').forEach(filter => {
                filter.addEventListener('click', function() {
                    // Usuń aktywną klasę z wszystkich filtrów
                    document.querySelectorAll('.category-filter').forEach(f => f.classList.remove('active'));
                    // Dodaj aktywną klasę do klikniętego filtru
                    this.classList.add('active');
                    
                    currentCategory = this.dataset.category;
                    renderConfigs();
                });
            });
        }

        // Konfiguracja pomocy dla typów
        function setupTypeHelp() {
            document.getElementById('configType').addEventListener('change', function() {
                const valueHelp = document.getElementById('valueHelp');
                const type = this.value;
                
                switch(type) {
                    case 'string':
                        valueHelp.textContent = 'Wprowadź tekst, np. "localhost" lub "production"';
                        break;
                    case 'integer':
                        valueHelp.textContent = 'Wprowadź liczbę całkowitą, np. 60 lub 1000';
                        break;
                    case 'float':
                        valueHelp.textContent = 'Wprowadź liczbę dziesiętną, np. 2.5 lub 0.1';
                        break;
                    case 'boolean':
                        valueHelp.textContent = 'Wprowadź "true" lub "false"';
                        break;
                }
            });
        }

        // Ładowanie konfiguracji
        async function loadConfigs() {
            try {
                const response = await fetch('/api/config');
                if (!response.ok) throw new Error('Błąd ładowania konfiguracji');
                
                configs = await response.json();
                renderConfigs();
            } catch (error) {
                console.error('Error loading configs:', error);
                showError('Błąd ładowania konfiguracji: ' + error.message);
            }
        }

        // Kategoryzacja konfiguracji
        function categorizeConfig(key) {
            const lowerKey = key.toLowerCase();
            
            if (lowerKey.includes('api') || lowerKey.includes('key') || lowerKey.includes('secret')) {
                return 'api';
            } else if (lowerKey.includes('notification') || lowerKey.includes('alert') || lowerKey.includes('email')) {
                return 'notifications';
            } else if (lowerKey.includes('timeout') || lowerKey.includes('interval') || lowerKey.includes('cache') || lowerKey.includes('limit')) {
                return 'performance';
            } else {
                return 'system';
            }
        }

        // Renderowanie konfiguracji
        function renderConfigs() {
            const container = document.getElementById('configsContainer');
            
            // Filtruj konfiguracje według kategorii
            let filteredConfigs = configs;
            if (currentCategory !== 'all') {
                filteredConfigs = configs.filter(config => categorizeConfig(config.key) === currentCategory);
            }
            
            if (filteredConfigs.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="config-card">
                            <div class="card-body py-5">
                                <i class="fas fa-cog fa-3x text-muted mb-3"></i>
                                <h5>Brak konfiguracji</h5>
                                <p class="text-muted">
                                    ${currentCategory === 'all' ? 'Dodaj pierwszą konfigurację.' : `Brak konfiguracji w kategorii "${currentCategory}".`}
                                </p>
                                <button class="btn btn-config" data-bs-toggle="modal" data-bs-target="#configModal" onclick="openCreateModal()">
                                    <i class="fas fa-plus"></i>
                                    Dodaj konfigurację
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredConfigs.map(config => `
                <div class="col-md-6 col-lg-4">
                    <div class="config-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="config-key">
                                ${config.key}
                                ${config.is_sensitive ? '<span class="sensitive-badge">WRAŻLIWE</span>' : ''}
                            </div>
                            <span class="config-type-badge type-${config.config_type}">
                                ${config.config_type}
                            </span>
                        </div>
                        
                        <div class="config-value">
                            <strong>Wartość:</strong> 
                            ${config.is_sensitive && config.value ? '********' : (config.value || '<em>brak</em>')}
                        </div>
                        
                        ${config.description ? `
                            <div class="config-description">
                                ${config.description}
                            </div>
                        ` : ''}
                        
                        <div class="d-flex gap-2 mt-3">
                            <button class="btn btn-admin btn-sm flex-fill" onclick="editConfig(${config.id})">
                                <i class="fas fa-edit"></i>
                                Edytuj
                            </button>
                            <button class="btn btn-admin-danger btn-sm" onclick="deleteConfig(${config.id}, '${config.key}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Otwórz modal tworzenia
        function openCreateModal() {
            currentConfigId = null;
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus"></i> Dodaj konfigurację';
            document.getElementById('configForm').reset();
            document.getElementById('configId').value = '';
            document.getElementById('saveButtonText').textContent = 'Dodaj konfigurację';
            document.getElementById('valueHelp').textContent = 'Wprowadź wartość zgodną z wybranym typem';
        }

        // Edytuj konfigurację
        async function editConfig(id) {
            try {
                const config = configs.find(c => c.id === id);
                if (!config) throw new Error('Konfiguracja nie znaleziona');
                
                currentConfigId = id;
                document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit"></i> Edytuj konfigurację';
                document.getElementById('configId').value = id;
                document.getElementById('configKey').value = config.key;
                document.getElementById('configValue').value = config.is_sensitive ? '' : config.value;
                document.getElementById('configDescription').value = config.description || '';
                document.getElementById('configType').value = config.config_type;
                document.getElementById('isSensitive').checked = config.is_sensitive;
                document.getElementById('saveButtonText').textContent = 'Zaktualizuj konfigurację';
                
                // Ustaw placeholder dla wrażliwych danych
                if (config.is_sensitive) {
                    document.getElementById('configValue').placeholder = 'Pozostaw puste aby zachować obecną wartość';
                }
                
                new bootstrap.Modal(document.getElementById('configModal')).show();
            } catch (error) {
                console.error('Error loading config:', error);
                showError('Błąd ładowania konfiguracji: ' + error.message);
            }
        }

        // Zapisz konfigurację
        async function saveConfig() {
            const form = document.getElementById('configForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const saveButton = document.querySelector('#configModal .btn-config');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<span class="loading-spinner"></span> Zapisywanie...';
            saveButton.disabled = true;

            try {
                const configData = {
                    key: document.getElementById('configKey').value,
                    value: document.getElementById('configValue').value,
                    description: document.getElementById('configDescription').value,
                    config_type: document.getElementById('configType').value,
                    is_sensitive: document.getElementById('isSensitive').checked
                };

                const url = currentConfigId ? `/api/config/${currentConfigId}` : '/api/config';
                const method = currentConfigId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Błąd zapisywania konfiguracji');
                }

                bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
                showSuccess(currentConfigId ? 'Konfiguracja zaktualizowana pomyślnie' : 'Konfiguracja dodana pomyślnie');
                loadConfigs();

            } catch (error) {
                console.error('Error saving config:', error);
                showError('Błąd zapisywania konfiguracji: ' + error.message);
            } finally {
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
            }
        }

        // Usuń konfigurację
        function deleteConfig(id, key) {
            deleteConfigId = id;
            document.getElementById('deleteConfigKey').textContent = key;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Potwierdź usunięcie
        async function confirmDelete() {
            try {
                const response = await fetch(`/api/config/${deleteConfigId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Błąd usuwania konfiguracji');
                }

                bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                showSuccess('Konfiguracja usunięta pomyślnie');
                loadConfigs();

            } catch (error) {
                console.error('Error deleting config:', error);
                showError('Błąd usuwania konfiguracji: ' + error.message);
            }
        }

        // Funkcje pomocnicze
        function showSuccess(message) {
            console.log('Success:', message);
            // TODO: Implementacja toast notification
        }

        function showError(message) {
            console.error('Error:', message);
            alert(message); // Tymczasowe rozwiązanie
        }
    </script>
</body>
</html>
