#!/usr/bin/env python3
"""
Skrypt do dodania przykładowych danych do panelu administratora.
Uruchom ten skrypt, aby dodać przykładową strategię i kanał do testowania.
"""

import os
import sys
from datetime import datetime

# Dodaj ścieżkę do modeli
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import DatabaseManager
from models.strategy import StrategyManager, Strategy
from models.channel import ChannelManager, DiscordChannel
from models.config import ConfigManager, SystemConfig

def setup_demo_data():
    """Dodaje przykładowe dane do panelu administratora."""

    # Ścieżka do bazy danych
    db_path = 'trading_signals.db'

    print("🔧 Inicjalizacja bazy danych...")
    db_manager = DatabaseManager(db_path)

    # Inicjalizacja managerów
    strategy_manager = StrategyManager(db_manager)
    channel_manager = ChannelManager(db_manager)
    config_manager = ConfigManager(db_manager)

    print("📊 Dodawanie przykładowej strategii...")

    # Dodaj przykładową strategię
    demo_strategy = Strategy(
        name="Demo Scalping Strategy",
        description="Przykładowa strategia scalpingowa do testowania systemu",
        timeframe_min=60,
        risk_percent=2.0,
        max_signals_per_day=10,
        signal_validity_hours=48,
        min_timeframe_minutes=15,
        default_timeframe_multiplier=60,
        is_active=True
    )

    try:
        strategy_id = strategy_manager.create_strategy(demo_strategy, user_ip="127.0.0.1")
        print(f"✅ Strategia utworzona z ID: {strategy_id}")
    except Exception as e:
        print(f"⚠️ Strategia już istnieje lub błąd: {e}")
        # Pobierz istniejącą strategię
        strategies = strategy_manager.get_all_strategies()
        if strategies:
            strategy_id = strategies[0].id
            print(f"📋 Używam istniejącej strategii z ID: {strategy_id}")
        else:
            print("❌ Nie można utworzyć ani znaleźć strategii")
            return

    print("📺 Dodawanie przykładowego kanału Discord...")

    # Dodaj przykładowy kanał Discord
    demo_channel = DiscordChannel(
        channel_id="123456789012345678",  # Przykładowe ID kanału Discord
        channel_name="demo-trading-signals",
        guild_id="987654321098765432",   # Przykładowe ID serwera Discord
        guild_name="Demo Trading Server",
        strategy_id=strategy_id,
        allow_bot_messages=True,
        is_active=True
    )

    try:
        channel_id = channel_manager.create_channel(demo_channel, user_ip="127.0.0.1")
        print(f"✅ Kanał utworzony z ID: {channel_id}")

        # Dodaj przykładowego bota do whitelisty
        print("🤖 Dodawanie bota do whitelisty...")
        bot_id = channel_manager.add_bot_to_whitelist(
            channel_id=channel_id,
            bot_discord_id="111222333444555666",  # Przykładowe ID bota
            bot_name="Demo Trading Bot",
            user_ip="127.0.0.1"
        )
        print(f"✅ Bot dodany do whitelisty z ID: {bot_id}")

    except Exception as e:
        print(f"⚠️ Kanał już istnieje lub błąd: {e}")

    print("⚙️ Dodawanie przykładowej konfiguracji...")

    # Dodaj przykładową konfigurację Discord
    demo_configs = [
        SystemConfig(
            key="discord_token",
            value="YOUR_DISCORD_BOT_TOKEN_HERE",
            description="Token bota Discord do łączenia się z serwerami",
            config_type="string",
            is_sensitive=True
        ),
        SystemConfig(
            key="bybit_api_key",
            value="YOUR_BYBIT_API_KEY_HERE",
            description="Klucz API Bybit do pobierania danych cenowych",
            config_type="string",
            is_sensitive=True
        ),
        SystemConfig(
            key="price_check_interval",
            value="60",
            description="Interwał sprawdzania cen w sekundach",
            config_type="integer",
            is_sensitive=False
        ),
        SystemConfig(
            key="signal_timeout_hours",
            value="48",
            description="Czas ważności sygnału w godzinach",
            config_type="integer",
            is_sensitive=False
        )
    ]

    for config in demo_configs:
        try:
            config_id = config_manager.create_config(config, user_ip="127.0.0.1")
            print(f"✅ Konfiguracja '{config.key}' utworzona z ID: {config_id}")
        except Exception as e:
            print(f"⚠️ Konfiguracja '{config.key}' już istnieje lub błąd: {e}")

    print("\n🎉 Przykładowe dane zostały dodane do panelu administratora!")
    print("\n📋 Podsumowanie:")
    print("   • Strategia: Demo Scalping Strategy")
    print("   • Kanał Discord: demo-trading-signals")
    print("   • Bot: Demo Trading Bot (na whiteliście)")
    print("   • Konfiguracje: Discord token, Bybit API, interwały")
    print("\n🌐 Otwórz panel administratora: http://localhost:5001")
    print("   • Przejdź do 'Kanały Discord'")
    print("   • Kliknij przycisk 'Test' przy kanale demo")
    print("   • Sprawdź wyniki testowania połączenia")
    print("\n⚠️ UWAGA: Aby test połączenia działał, musisz:")
    print("   1. Skonfigurować prawdziwy token Discord w 'Konfiguracja'")
    print("   2. Zmienić ID kanału na prawdziwe ID z Twojego serwera Discord")

if __name__ == "__main__":
    setup_demo_data()
