"""
Pytest Configuration
====================

Shared fixtures and configuration for all tests.
"""

import pytest
import sqlite3
import tempfile
import os
from unittest.mock import Mock, patch


@pytest.fixture
def test_db():
    """Create a temporary test database."""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
        db_path = tmp.name
    
    # Create test database with sample data
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create signals table
    cursor.execute('''
        CREATE TABLE signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            pair TEXT NOT NULL,
            direction TEXT NOT NULL,
            entry_price REAL,
            tp_price REAL,
            sl_price REAL,
            status TEXT DEFAULT 'NEW',
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            pnl REAL,
            pnl_percent REAL,
            timeframe TEXT,
            message_content TEXT
        )
    ''')
    
    # Insert sample test data
    test_signals = [
        ('BTCUSDT', 'LONG', 45000.0, 46000.0, 44000.0, 'TP_HIT', '2024-01-01 10:00:00', 100.0, 2.22, '1H', 'Test signal 1'),
        ('ETHUSDT', 'SHORT', 3000.0, 2900.0, 3100.0, 'SL_HIT', '2024-01-01 11:00:00', -50.0, -1.67, '4H', 'Test signal 2'),
        ('ADAUSDT', 'LONG', 0.5, 0.52, 0.48, 'NEW', '2024-01-01 12:00:00', None, None, '1D', 'Test signal 3'),
        ('SOLUSDT', 'SHORT', 100.0, 95.0, 105.0, 'ENTRY_HIT', '2024-01-01 13:00:00', None, None, '15m', 'Test signal 4'),
        ('DOTUSDT', 'LONG', 8.0, 8.5, 7.5, 'EXPIRED', '2024-01-01 14:00:00', 0.0, 0.0, '1H', 'Test signal 5'),
    ]
    
    cursor.executemany('''
        INSERT INTO signals (pair, direction, entry_price, tp_price, sl_price, status, timestamp, pnl, pnl_percent, timeframe, message_content)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', test_signals)
    
    conn.commit()
    conn.close()
    
    yield db_path
    
    # Cleanup
    os.unlink(db_path)


@pytest.fixture
def mock_bybit_api():
    """Mock Bybit API responses."""
    with patch('pybit.unified_trading.HTTP') as mock_http:
        mock_instance = Mock()
        mock_http.return_value = mock_instance
        
        # Mock successful ticker response
        mock_instance.get_tickers.return_value = {
            'result': {
                'list': [{'symbol': 'BTCUSDT', 'lastPrice': '45000.0'}]
            }
        }
        
        # Mock successful kline response
        mock_instance.get_kline.return_value = {
            'result': {
                'list': [
                    ['1640995200000', '45000.0', '46000.0', '44000.0', '45500.0', '1000.0', '45250000.0']
                ]
            }
        }
        
        yield mock_instance


@pytest.fixture
def base_url():
    """Base URL for API tests."""
    return "http://localhost:5000"


@pytest.fixture
def sample_signal_data():
    """Sample signal data for testing."""
    return {
        'id': 1,
        'pair': 'BTCUSDT',
        'direction': 'LONG',
        'entry_price': 45000.0,
        'tp_price': 46000.0,
        'sl_price': 44000.0,
        'status': 'NEW',
        'timestamp': '2024-01-01 10:00:00',
        'pnl': None,
        'pnl_percent': None,
        'timeframe': '1H',
        'message_content': 'Test signal'
    }


@pytest.fixture
def sample_discord_message():
    """Sample Discord message for signal parsing tests."""
    return """
🚀 SIGNAL ALERT 🚀

📈 LONG BTCUSDT
💰 Entry: 45000
🎯 TP: 46000
🛑 SL: 44000
⏰ Timeframe: 1H
    """


# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.api = pytest.mark.api
pytest.mark.debug = pytest.mark.debug
pytest.mark.slow = pytest.mark.slow
