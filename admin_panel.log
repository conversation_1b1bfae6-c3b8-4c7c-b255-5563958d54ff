2025-06-16 03:23:19,564 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-16 03:23:19,564 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-16 03:23:19,792 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:23:20,315 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:23:20,333 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:23:40,712 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\werkzeug\\debug\\__init__.py', reloading
2025-06-16 03:23:40,747 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\werkzeug\\serving.py', reloading
2025-06-16 03:23:40,770 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:40] "GET / HTTP/1.1" 200 -
2025-06-16 03:23:41,144 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:23:41,659 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:23:42,227 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:23:42,244 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:23:42,445 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-16 03:23:48,619 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:48] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:23:58,123 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:58] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:24:11,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:24:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:24:32,562 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\start_servers.py', reloading
2025-06-16 03:24:32,568 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\start_servers.py', reloading
2025-06-16 03:24:32,743 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:24:33,061 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:24:33,068 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:24:41,652 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:24:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:25:11,339 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:25:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:25:41,644 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:25:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:11,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:23,980 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:23] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:26:24,038 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:24] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:26:25,984 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:25] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:26,297 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:26] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:26,493 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:26] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:27,368 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:26:27,566 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:26:27,729 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:26:30,082 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:30] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:30,127 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:27:00,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:27:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:27:30,346 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:27:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:28:00,651 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:28:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:28:30,350 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:28:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:00,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:29:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:21,046 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:21,047 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:21,774 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:22,083 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:22,093 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:29:30,646 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:29:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:34,580 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:34,581 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:35,184 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:35,446 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:35,456 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:29:54,519 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:54,519 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:54,662 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:55,022 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:55,035 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:30:13,426 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:13,427 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:13,428 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:14,205 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:30:14,518 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:30:14,531 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:30:43,646 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:30:43] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:30:44,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:44,361 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:44,813 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:30:45,110 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:30:45,123 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:31:05,781 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:05,782 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:05,784 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:06,375 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:31:06,838 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:31:06,852 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:31:44,656 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:31:44] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:32:45,652 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:32:45] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:33:46,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:33:46] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:34:47,647 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:34:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:35:47,644 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:35:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:35:59,179 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:35:59] "GET / HTTP/1.1" 200 -
2025-06-16 03:35:59,467 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:35:59] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:02,404 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:02] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:36:02,486 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:02] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:19,711 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:19] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:36:19,788 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:19] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:36:19,789 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:19] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:29,667 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:29] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:30,035 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:30] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:36:30,333 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:30] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:47,669 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:48,301 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:48] "[35m[1mPOST /api/strategies HTTP/1.1[0m" 201 -
2025-06-16 03:36:48,624 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:48] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:51,282 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:51] "GET / HTTP/1.1" 200 -
2025-06-16 03:36:51,570 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:51] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:52,533 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:52] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:36:52,601 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:52] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:36:52,789 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:52] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:37:26,949 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:26] "[35m[1mPOST /api/channels HTTP/1.1[0m" 201 -
2025-06-16 03:37:27,277 - __main__ - ERROR - API error in api_get_channels: 'str' object has no attribute 'isoformat'
2025-06-16 03:37:27,279 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:27] "[35m[1mGET /api/channels HTTP/1.1[0m" 500 -
2025-06-16 03:37:30,125 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:30] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-16 03:37:47,820 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:38:47,342 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:38:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:39:47,657 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:39:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:40:47,343 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:40:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:41:47,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:41:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:42:47,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:42:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:43:47,667 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:43:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:44:47,349 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:44:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:45:41,062 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:41,064 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:41,953 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:45:44,381 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:45:44,392 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:45:47,645 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:45:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:45:55,808 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:55,811 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:56,678 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:45:57,079 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:45:57,092 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:46:09,371 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:09,373 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:10,255 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:46:10,606 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:46:10,617 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:46:23,001 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:23,002 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:23,797 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:46:24,253 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:46:24,270 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:46:33,449 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:46:33] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:46:33,553 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:46:33] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:46:33,555 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:46:33] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:46:47,643 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:46:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:47:47,340 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:47:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:48:47,656 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:48:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:48:53,798 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\admin_panel.py', reloading
2025-06-16 03:48:53,800 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\admin_panel.py', reloading
2025-06-16 03:48:54,487 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:48:54,895 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:48:54,906 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:49:47,655 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:49:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:50:47,343 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:50:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:51:17,665 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:51:17] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:51:17,745 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:51:17] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:51:17,913 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:51:17] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:51:38,025 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\admin_panel.py', reloading
2025-06-16 03:51:38,025 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\admin_panel.py', reloading
2025-06-16 03:51:38,187 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:51:38,490 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:51:38,498 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:51:47,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:51:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:51:59,333 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\admin_panel.py', reloading
2025-06-16 03:51:59,337 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\admin_panel.py', reloading
2025-06-16 03:51:59,733 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:52:00,100 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:52:00,116 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:52:21,260 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:52:21] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:52:21,326 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:52:21] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:52:34,036 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\netrc.py', reloading
2025-06-16 03:52:34,533 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:52:35,166 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:52:35,179 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:52:37,056 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\zoneinfo\\__init__.py', reloading
2025-06-16 03:52:37,061 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\zoneinfo\\_tzpath.py', reloading
2025-06-16 03:52:37,069 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\zoneinfo\\_common.py', reloading
2025-06-16 03:52:37,416 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:52:38,064 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:52:38,079 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:52:41,854 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:52:41] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-16 03:52:47,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:52:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:52:55,220 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\setup_demo_data.py', reloading
2025-06-16 03:52:55,223 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\setup_demo_data.py', reloading
2025-06-16 03:52:56,265 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:52:56,571 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:52:56,579 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:53:24,274 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\setup_demo_data.py', reloading
2025-06-16 03:53:24,275 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\setup_demo_data.py', reloading
2025-06-16 03:53:24,817 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:53:25,136 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:53:25,149 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:53:47,662 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:53:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:54:02,448 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:54:02] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:54:02,732 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:54:02] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:54:02,841 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:54:02] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:54:10,046 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\util.py', reloading
2025-06-16 03:54:10,590 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:54:11,192 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:54:11,213 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:54:47,641 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:54:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:55:47,344 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:55:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:56:47,642 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:56:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:57:47,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:57:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:58:47,665 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:58:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:59:47,343 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:59:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:00:47,655 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:00:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:01:47,352 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:01:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:02:47,655 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:02:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:03:47,339 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:03:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:04:47,642 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:04:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:05:47,657 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:05:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:06:47,651 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:06:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:07:47,654 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:07:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:08:47,351 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:08:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:09:47,655 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:09:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:10:47,644 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:10:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:11:47,344 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:11:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:12:47,659 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:12:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:13:22,123 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-06-16 04:13:22,125 - discord.client - INFO - logging in using static token
2025-06-16 04:13:24,997 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 46a8b84654f0e50ededb23043886acc1).
2025-06-16 04:13:27,295 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:13:27] "POST /api/channels/1/test HTTP/1.1" 200 -
2025-06-16 04:13:47,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:13:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:14:47,342 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:14:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:15:47,641 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:15:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:15:59,899 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:15:59] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 04:16:12,968 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:16:12] "GET / HTTP/1.1" 200 -
2025-06-16 04:16:13,066 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:16:13] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:16:20,600 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:16:20] "GET /config HTTP/1.1" 200 -
2025-06-16 04:16:20,864 - __main__ - ERROR - API error in api_get_configs: 'str' object has no attribute 'isoformat'
2025-06-16 04:16:20,865 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:16:20] "[35m[1mGET /api/config HTTP/1.1[0m" 500 -
2025-06-16 04:16:35,547 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x000002AB3343BFB0>, 77259.921)]']
connector: <aiohttp.connector.TCPConnector object at 0x000002AB3304E570>
2025-06-16 04:16:47,648 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:16:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:16:48,961 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:16:48] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-16 04:17:47,649 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:17:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:18:47,340 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:18:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:19:47,655 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:19:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:20:47,347 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:20:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:21:47,639 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:21:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:22:47,350 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:22:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:23:47,654 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:23:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:24:47,338 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:24:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:25:47,654 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:25:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:26:47,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:26:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:27:47,652 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:27:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:28:47,348 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:28:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:29:47,657 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:29:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:30:47,353 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:30:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:31:47,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:31:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:32:47,348 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:32:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:33:47,655 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:33:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:34:47,339 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:34:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:35:47,656 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:35:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:36:47,350 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:36:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:37:47,656 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:37:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:38:47,332 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:38:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:39:47,649 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:39:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:40:47,337 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:40:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:41:47,647 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:41:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:42:47,334 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:42:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:43:47,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:43:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 04:44:36,825 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 04:44:36] "GET /api/health HTTP/1.1" 200 -
