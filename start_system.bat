@echo off
echo ========================================
echo 🚀 DISCORD BYBIT SIGNAL MONITOR
echo ========================================
echo.
echo 📊 Trading Dashboard: http://localhost:5000
echo 🔧 Admin Panel:       http://localhost:5001
echo.
echo 💡 Naciśnij Ctrl+C aby zatrzymać system
echo ========================================
echo.

REM Sprawdź czy Python jest zainstalowany
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python nie jest zainstalowany lub nie jest w PATH
    pause
    exit /b 1
)

REM Sprawdź czy pliki istnieją
if not exist "dashboard.py" (
    echo ❌ Nie znaleziono pliku dashboard.py
    pause
    exit /b 1
)

if not exist "admin_panel.py" (
    echo ❌ Nie znaleziono pliku admin_panel.py
    pause
    exit /b 1
)

REM Uruchom system
echo ✅ Uruchamianie systemu...
echo.

REM Uruchom oba serwery w osobnych oknach
start "Trading Dashboard" cmd /k "python dashboard.py"
timeout /t 3 /nobreak >nul
start "Admin Panel" cmd /k "python admin_panel.py"

echo ✅ System uruchomiony!
echo.
echo 🌐 Otwórz w przeglądarce:
echo    • Trading Dashboard: http://localhost:5000
echo    • Admin Panel:       http://localhost:5001
echo.
echo 📝 Aby zatrzymać system, zamknij okna serwerów
echo.
pause
