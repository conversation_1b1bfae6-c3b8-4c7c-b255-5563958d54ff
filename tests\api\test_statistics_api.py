#!/usr/bin/env python3
"""
Test ulepszeń statystyk - porównanie przed i po poprawkach.
Zorganizowany test z pytest framework.
"""

import pytest
import sqlite3
import pandas as pd
import numpy as np
import math
import requests
from datetime import datetime


class TestStatisticsAPI:
    """Test suite for Statistics API endpoints."""
    
    def test_statistics_endpoint_basic(self, base_url):
        """Test podstawowej funkcjonalności endpoint statystyk."""
        response = requests.get(f'{base_url}/api/statistics')
        assert response.status_code == 200
        
        stats = response.json()
        assert isinstance(stats, dict), "Statistics should return object"
        
        # Sprawdź kluczowe pola
        expected_fields = ['total_signals', 'win_rate', 'total_pnl']
        for field in expected_fields:
            if field in stats:
                assert isinstance(stats[field], (int, float)), f"{field} should be numeric"
    
    def test_statistics_calculation_accuracy(self, base_url, test_db):
        """Test dokładności obliczeń statystyk."""
        # Pobierz statystyki z API
        response = requests.get(f'{base_url}/api/statistics')
        assert response.status_code == 200
        api_stats = response.json()
        
        # Oblicz statystyki bezpośrednio z bazy
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        # Zamknięte sygnały
        cursor.execute("SELECT COUNT(*) as total FROM signals WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')")
        closed_signals = cursor.fetchone()[0]
        
        # Wygrane sygnały
        cursor.execute("SELECT COUNT(*) as wins FROM signals WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl > 0")
        winning_signals = cursor.fetchone()[0]
        
        # Win rate
        expected_win_rate = (winning_signals / closed_signals * 100) if closed_signals > 0 else 0
        
        conn.close()
        
        # Porównaj z API
        if 'win_rate' in api_stats:
            assert abs(api_stats['win_rate'] - expected_win_rate) < 0.1, \
                f"Win rate mismatch: API={api_stats['win_rate']}, Expected={expected_win_rate}"
    
    def test_pair_statistics(self, base_url):
        """Test statystyk per para."""
        response = requests.get(f'{base_url}/api/statistics')
        assert response.status_code == 200
        
        stats = response.json()
        
        if 'pair_stats' in stats:
            pair_stats = stats['pair_stats']
            assert isinstance(pair_stats, (list, dict)), "Pair stats should be list or dict"
            
            if isinstance(pair_stats, list) and pair_stats:
                for pair_stat in pair_stats[:3]:  # Check first 3
                    assert 'pair' in pair_stat or 'symbol' in pair_stat, "Pair stat should have pair/symbol"
                    assert 'count' in pair_stat or 'total_signals' in pair_stat, "Pair stat should have count"
    
    @pytest.mark.slow
    def test_advanced_statistics_calculation(self, test_db):
        """Test zaawansowanych obliczeń statystyk."""
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        print(f"\n🔬 TEST ZAAWANSOWANYCH METRYK:")
        
        # Pobierz dane PnL dla zamkniętych sygnałów
        cursor.execute("""
            SELECT pnl 
            FROM signals 
            WHERE pnl IS NOT NULL 
              AND status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
            ORDER BY timestamp
        """)
        
        pnl_data = [row[0] for row in cursor.fetchall()]
        
        if pnl_data and len(pnl_data) >= 2:
            pnl_series = pd.Series(pnl_data)
            
            # Sharpe Ratio
            pnl_mean = pnl_series.mean()
            pnl_std = pnl_series.std()
            
            if pnl_std > 0:
                sharpe_ratio = pnl_mean / pnl_std
                annualized_sharpe = sharpe_ratio * np.sqrt(252)
                print(f"  Sharpe Ratio (daily): {sharpe_ratio:.4f}")
                print(f"  Sharpe Ratio (annualized): {annualized_sharpe:.4f}")
                
                assert not math.isnan(sharpe_ratio), "Sharpe ratio should not be NaN"
                assert not math.isinf(sharpe_ratio), "Sharpe ratio should not be infinite"
            
            # Max Drawdown
            cumulative_pnl = pnl_series.cumsum()
            running_max = cumulative_pnl.expanding().max()
            drawdown = (cumulative_pnl - running_max) / running_max.where(running_max != 0, 1)
            max_drawdown = abs(drawdown.min()) if not drawdown.empty else 0.0
            print(f"  Max Drawdown: {max_drawdown:.4f} ({max_drawdown*100:.2f}%)")
            
            assert max_drawdown >= 0, "Max drawdown should be non-negative"
            assert max_drawdown <= 1, "Max drawdown should not exceed 100%"
            
            # Profit Factor
            total_wins = pnl_series[pnl_series > 0].sum()
            total_losses = abs(pnl_series[pnl_series < 0].sum())
            
            if total_losses > 0:
                profit_factor = total_wins / total_losses
                print(f"  Profit Factor: {profit_factor:.4f}")
                assert profit_factor > 0, "Profit factor should be positive"
        
        conn.close()


def test_old_vs_new_stats_manual():
    """Porównuje stare i nowe obliczenia statystyk (kompatybilność wsteczna)."""
    conn = sqlite3.connect('signals.db')
    conn.row_factory = sqlite3.Row
    
    print("=== PORÓWNANIE STARYCH I NOWYCH STATYSTYK ===\n")
    
    # === STARE OBLICZENIA (błędne) ===
    print("🔴 STARE OBLICZENIA (błędne):")
    
    # Stary Win Rate - wszystkie sygnały z PnL
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) as total FROM signals WHERE status NOT IN ('NEW', 'ENTRY_HIT')")
    old_closed = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as wins FROM signals WHERE pnl > 0")
    old_wins = cursor.fetchone()['wins']
    
    old_win_rate = (old_wins / old_closed * 100) if old_closed > 0 else 0
    print(f"  Stary Win Rate: {old_win_rate:.2f}% ({old_wins}/{old_closed})")
    
    # === NOWE OBLICZENIA (poprawne) ===
    print("\n🟢 NOWE OBLICZENIA (poprawne):")
    
    # Nowy Win Rate - tylko zamknięte sygnały z PnL
    cursor.execute("SELECT COUNT(*) as total FROM signals WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')")
    new_closed = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as wins FROM signals WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED') AND pnl > 0")
    new_wins = cursor.fetchone()['wins']
    
    new_win_rate = (new_wins / new_closed * 100) if new_closed > 0 else 0
    print(f"  Nowy Win Rate: {new_win_rate:.2f}% ({new_wins}/{new_closed})")
    
    # === SZCZEGÓŁOWA ANALIZA ===
    print(f"\n📊 SZCZEGÓŁOWA ANALIZA:")
    print(f"  Różnica w liczbie zamkniętych: {new_closed - old_closed}")
    print(f"  Różnica w liczbie wygranych: {new_wins - old_wins}")
    print(f"  Różnica w Win Rate: {new_win_rate - old_win_rate:.2f} punktów procentowych")
    
    # === TEST ZAAWANSOWANYCH METRYK ===
    print(f"\n🔬 TEST ZAAWANSOWANYCH METRYK:")
    
    # Pobierz dane PnL dla zamkniętych sygnałów
    cursor.execute("""
        SELECT pnl 
        FROM signals 
        WHERE pnl IS NOT NULL 
          AND status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
        ORDER BY timestamp
    """)
    
    pnl_data = [row['pnl'] for row in cursor.fetchall()]
    
    if pnl_data and len(pnl_data) >= 2:
        pnl_series = pd.Series(pnl_data)
        
        # Sharpe Ratio
        pnl_mean = pnl_series.mean()
        pnl_std = pnl_series.std()
        
        if pnl_std > 0:
            sharpe_ratio = pnl_mean / pnl_std
            annualized_sharpe = sharpe_ratio * np.sqrt(252)
            print(f"  Sharpe Ratio (daily): {sharpe_ratio:.4f}")
            print(f"  Sharpe Ratio (annualized): {annualized_sharpe:.4f}")
        else:
            print(f"  Sharpe Ratio: Nie można obliczyć (std = 0)")
        
        # Sortino Ratio
        downside_returns = pnl_series[pnl_series < 0]
        if len(downside_returns) > 0:
            downside_std = downside_returns.std()
            if downside_std > 0:
                sortino_ratio = pnl_mean / downside_std * np.sqrt(252)
                print(f"  Sortino Ratio: {sortino_ratio:.4f}")
            else:
                print(f"  Sortino Ratio: ∞ (brak strat)")
        else:
            print(f"  Sortino Ratio: ∞ (brak strat)")
        
        # Max Drawdown
        cumulative_pnl = pnl_series.cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = (cumulative_pnl - running_max) / running_max.where(running_max != 0, 1)
        max_drawdown = abs(drawdown.min()) if not drawdown.empty else 0.0
        print(f"  Max Drawdown: {max_drawdown:.4f} ({max_drawdown*100:.2f}%)")
        
        # Calmar Ratio
        annual_return = pnl_mean * 252
        calmar_ratio = (annual_return / max_drawdown) if max_drawdown > 0 else 0.0
        print(f"  Calmar Ratio: {calmar_ratio:.4f}")
        
        # Profit Factor
        total_wins = pnl_series[pnl_series > 0].sum()
        total_losses = abs(pnl_series[pnl_series < 0].sum())
        
        if total_losses > 0:
            profit_factor = total_wins / total_losses
            print(f"  Profit Factor: {profit_factor:.4f}")
        else:
            print(f"  Profit Factor: ∞ (brak strat)")
        
        # Średnie zyski/straty
        avg_win = pnl_series[pnl_series > 0].mean() if (pnl_series > 0).any() else 0.0
        avg_loss = pnl_series[pnl_series < 0].mean() if (pnl_series < 0).any() else 0.0
        
        print(f"  Średni zysk: {avg_win:.4f} ({avg_win*100:.2f}%)")
        print(f"  Średnia strata: {avg_loss:.4f} ({avg_loss*100:.2f}%)")
        
        # Consecutive wins/losses
        wins_losses = (pnl_series > 0).astype(int)
        consecutive_wins = max_consecutive(wins_losses, 1)
        consecutive_losses = max_consecutive(wins_losses, 0)
        
        print(f"  Max kolejne wygrane: {consecutive_wins}")
        print(f"  Max kolejne przegrane: {consecutive_losses}")
    
    else:
        print("  Brak wystarczających danych do obliczeń zaawansowanych")
    
    # === STATYSTYKI PER PARA ===
    print(f"\n💱 STATYSTYKI PER PARA (poprawione):")
    cursor.execute("""
        SELECT pair,
               COUNT(*) as count,
               COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
               AVG(pnl) as avg_pnl,
               SUM(pnl) as total_pnl,
               ROUND((COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 / COUNT(*)), 1) as win_rate
        FROM signals
        WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')
        GROUP BY pair
        ORDER BY count DESC
        LIMIT 5
    """)
    
    pair_stats = cursor.fetchall()
    for row in pair_stats:
        print(f"  {row['pair']}: {row['count']} sygnałów, WR: {row['win_rate']}%, Avg PnL: {row['avg_pnl']:.4f}")
    
    conn.close()
    print(f"\n=== KONIEC TESTU ===")


def max_consecutive(series, value):
    """Oblicz maksymalną liczbę kolejnych wystąpień wartości."""
    if series.empty:
        return 0
    
    consecutive = 0
    max_consecutive_count = 0
    
    for val in series:
        if val == value:
            consecutive += 1
            max_consecutive_count = max(max_consecutive_count, consecutive)
        else:
            consecutive = 0
    
    return max_consecutive_count


if __name__ == "__main__":
    test_old_vs_new_stats_manual()
