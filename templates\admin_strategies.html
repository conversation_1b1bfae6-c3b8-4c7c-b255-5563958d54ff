<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📈 Zarządzan<PERSON> Strategiami - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-accent: #3498db;
            --admin-success: #27ae60;
            --admin-warning: #f39c12;
            --admin-danger: #e74c3c;
            --admin-dark: #1a252f;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-navbar {
            background: var(--admin-dark) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .admin-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
        }

        .strategy-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .strategy-card:hover {
            transform: translateY(-5px);
        }

        .strategy-card .card-header {
            background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--admin-accent) 0%, #5dade2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            color: white;
        }

        .btn-admin-success {
            background: linear-gradient(135deg, var(--admin-success) 0%, #58d68d 100%);
        }

        .btn-admin-danger {
            background: linear-gradient(135deg, var(--admin-danger) 0%, #ec7063 100%);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: var(--admin-success);
            color: white;
        }

        .status-inactive {
            background: var(--admin-danger);
            color: white;
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--admin-accent);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="fas fa-chart-line"></i>
                Zarządzanie Strategiami
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-light" href="/">
                    <i class="fas fa-arrow-left"></i>
                    Powrót do Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="admin-content fade-in">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0">
                    <i class="fas fa-chart-line text-primary"></i>
                    Zarządzanie Strategiami
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-admin" onclick="loadStrategies()">
                        <i class="fas fa-sync-alt"></i>
                        Odśwież
                    </button>
                    <button class="btn btn-admin-success" data-bs-toggle="modal" data-bs-target="#strategyModal" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i>
                        Dodaj strategię
                    </button>
                </div>
            </div>

            <!-- Strategies List -->
            <div class="row" id="strategiesContainer">
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Ładowanie...</span>
                    </div>
                    <p class="mt-2">Ładowanie strategii...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Strategy Modal -->
    <div class="modal fade" id="strategyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">
                        <i class="fas fa-chart-line"></i>
                        Dodaj strategię
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="strategyForm">
                        <input type="hidden" id="strategyId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="strategyName" class="form-label">Nazwa strategii *</label>
                                    <input type="text" class="form-control" id="strategyName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timeframeMin" class="form-label">Timeframe (minuty) *</label>
                                    <input type="number" class="form-control" id="timeframeMin" min="1" value="60" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Opis</label>
                            <textarea class="form-control" id="description" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="riskPercent" class="form-label">Ryzyko (%) *</label>
                                    <input type="number" class="form-control" id="riskPercent" min="0.1" max="10" step="0.1" value="2.0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxSignalsPerDay" class="form-label">Max sygnałów/dzień *</label>
                                    <input type="number" class="form-control" id="maxSignalsPerDay" min="1" max="100" value="10" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="signalValidityHours" class="form-label">Ważność sygnału (godziny) *</label>
                                    <input type="number" class="form-control" id="signalValidityHours" min="1" max="168" value="48" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="minTimeframeMinutes" class="form-label">Min timeframe (minuty) *</label>
                                    <input type="number" class="form-control" id="minTimeframeMinutes" min="1" value="15" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="defaultTimeframeMultiplier" class="form-label">Mnożnik timeframe *</label>
                                    <input type="number" class="form-control" id="defaultTimeframeMultiplier" min="1" value="60" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="isActive" class="form-label">Status</label>
                                    <select class="form-select" id="isActive">
                                        <option value="true">Aktywna</option>
                                        <option value="false">Nieaktywna</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="button" class="btn btn-admin" onclick="saveStrategy()">
                        <span id="saveButtonText">Zapisz strategię</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Potwierdź usunięcie
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Czy na pewno chcesz usunąć strategię <strong id="deleteStrategyName"></strong>?</p>
                    <p class="text-danger">
                        <i class="fas fa-warning"></i>
                        Ta akcja jest nieodwracalna!
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="button" class="btn btn-admin-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i>
                        Usuń strategię
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let strategies = [];
        let currentStrategyId = null;
        let deleteStrategyId = null;

        // Inicjalizacja
        document.addEventListener('DOMContentLoaded', function() {
            loadStrategies();
        });

        // Ładowanie strategii
        async function loadStrategies() {
            try {
                const response = await fetch('/api/strategies');
                if (!response.ok) throw new Error('Błąd ładowania strategii');
                
                strategies = await response.json();
                renderStrategies();
            } catch (error) {
                console.error('Error loading strategies:', error);
                showError('Błąd ładowania strategii: ' + error.message);
            }
        }

        // Renderowanie strategii
        function renderStrategies() {
            const container = document.getElementById('strategiesContainer');
            
            if (strategies.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center">
                        <div class="strategy-card">
                            <div class="card-body py-5">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5>Brak strategii</h5>
                                <p class="text-muted">Dodaj pierwszą strategię, aby rozpocząć.</p>
                                <button class="btn btn-admin-success" data-bs-toggle="modal" data-bs-target="#strategyModal" onclick="openCreateModal()">
                                    <i class="fas fa-plus"></i>
                                    Dodaj strategię
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = strategies.map(strategy => `
                <div class="col-md-6 col-lg-4">
                    <div class="strategy-card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">${strategy.name}</h6>
                                <span class="status-badge ${strategy.is_active ? 'status-active' : 'status-inactive'}">
                                    ${strategy.is_active ? 'Aktywna' : 'Nieaktywna'}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small mb-3">${strategy.description || 'Brak opisu'}</p>
                            
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Timeframe</small>
                                    <div class="fw-bold">${strategy.timeframe_min}m</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Ryzyko</small>
                                    <div class="fw-bold">${strategy.risk_percent}%</div>
                                </div>
                            </div>
                            
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Max sygnałów</small>
                                    <div class="fw-bold">${strategy.max_signals_per_day}/dzień</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Ważność</small>
                                    <div class="fw-bold">${strategy.signal_validity_hours}h</div>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button class="btn btn-admin btn-sm flex-fill" onclick="editStrategy(${strategy.id})">
                                    <i class="fas fa-edit"></i>
                                    Edytuj
                                </button>
                                <button class="btn btn-admin-danger btn-sm" onclick="deleteStrategy(${strategy.id}, '${strategy.name}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Otwórz modal tworzenia
        function openCreateModal() {
            currentStrategyId = null;
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus"></i> Dodaj strategię';
            document.getElementById('strategyForm').reset();
            document.getElementById('strategyId').value = '';
            document.getElementById('saveButtonText').textContent = 'Dodaj strategię';
        }

        // Edytuj strategię
        async function editStrategy(id) {
            try {
                const response = await fetch(`/api/strategies/${id}`);
                if (!response.ok) throw new Error('Błąd ładowania strategii');
                
                const strategy = await response.json();
                
                currentStrategyId = id;
                document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit"></i> Edytuj strategię';
                document.getElementById('strategyId').value = id;
                document.getElementById('strategyName').value = strategy.name;
                document.getElementById('description').value = strategy.description || '';
                document.getElementById('timeframeMin').value = strategy.timeframe_min;
                document.getElementById('riskPercent').value = strategy.risk_percent;
                document.getElementById('maxSignalsPerDay').value = strategy.max_signals_per_day;
                document.getElementById('signalValidityHours').value = strategy.signal_validity_hours;
                document.getElementById('minTimeframeMinutes').value = strategy.min_timeframe_minutes;
                document.getElementById('defaultTimeframeMultiplier').value = strategy.default_timeframe_multiplier;
                document.getElementById('isActive').value = strategy.is_active.toString();
                document.getElementById('saveButtonText').textContent = 'Zaktualizuj strategię';
                
                new bootstrap.Modal(document.getElementById('strategyModal')).show();
            } catch (error) {
                console.error('Error loading strategy:', error);
                showError('Błąd ładowania strategii: ' + error.message);
            }
        }

        // Zapisz strategię
        async function saveStrategy() {
            const form = document.getElementById('strategyForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const saveButton = document.querySelector('#strategyModal .btn-admin');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<span class="loading-spinner"></span> Zapisywanie...';
            saveButton.disabled = true;

            try {
                const strategyData = {
                    name: document.getElementById('strategyName').value,
                    description: document.getElementById('description').value,
                    timeframe_min: parseInt(document.getElementById('timeframeMin').value),
                    risk_percent: parseFloat(document.getElementById('riskPercent').value),
                    max_signals_per_day: parseInt(document.getElementById('maxSignalsPerDay').value),
                    signal_validity_hours: parseInt(document.getElementById('signalValidityHours').value),
                    min_timeframe_minutes: parseInt(document.getElementById('minTimeframeMinutes').value),
                    default_timeframe_multiplier: parseInt(document.getElementById('defaultTimeframeMultiplier').value),
                    is_active: document.getElementById('isActive').value === 'true'
                };

                const url = currentStrategyId ? `/api/strategies/${currentStrategyId}` : '/api/strategies';
                const method = currentStrategyId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(strategyData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Błąd zapisywania strategii');
                }

                bootstrap.Modal.getInstance(document.getElementById('strategyModal')).hide();
                showSuccess(currentStrategyId ? 'Strategia zaktualizowana pomyślnie' : 'Strategia dodana pomyślnie');
                loadStrategies();

            } catch (error) {
                console.error('Error saving strategy:', error);
                showError('Błąd zapisywania strategii: ' + error.message);
            } finally {
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
            }
        }

        // Usuń strategię
        function deleteStrategy(id, name) {
            deleteStrategyId = id;
            document.getElementById('deleteStrategyName').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Potwierdź usunięcie
        async function confirmDelete() {
            try {
                const response = await fetch(`/api/strategies/${deleteStrategyId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Błąd usuwania strategii');
                }

                bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                showSuccess('Strategia usunięta pomyślnie');
                loadStrategies();

            } catch (error) {
                console.error('Error deleting strategy:', error);
                showError('Błąd usuwania strategii: ' + error.message);
            }
        }

        // Funkcje pomocnicze
        function showSuccess(message) {
            // Implementacja toast notification
            console.log('Success:', message);
        }

        function showError(message) {
            // Implementacja toast notification
            console.error('Error:', message);
            alert(message); // Tymczasowe rozwiązanie
        }
    </script>
</body>
</html>
