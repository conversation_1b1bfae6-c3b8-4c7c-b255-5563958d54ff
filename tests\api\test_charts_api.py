#!/usr/bin/env python3
"""
Test wszystkich API endpoint dla wykresów.
Zorganizowany test z pytest framework.
"""

import pytest
import requests
import json
from typing import List, Dict, Any


class TestChartsAPI:
    """Test suite for Charts API endpoints."""
    
    @pytest.fixture
    def chart_endpoints(self):
        """Lista endpoint dla wykresów."""
        return [
            ("/api/statistics", "Statistics"),
            ("/api/pnl-chart", "PnL Chart"),
            ("/api/pnl-distribution", "PnL Distribution"),
            ("/api/signals?limit=5", "Signals"),
            ("/api/pairs", "Pairs")
        ]
    
    def test_all_chart_endpoints_status(self, base_url, chart_endpoints):
        """Test statusu wszystkich endpoint dla wykresów."""
        for endpoint, name in chart_endpoints:
            response = requests.get(f'{base_url}{endpoint}')
            assert response.status_code == 200, f"{name} endpoint failed with {response.status_code}"
    
    def test_statistics_endpoint(self, base_url):
        """Test endpoint statystyk."""
        response = requests.get(f'{base_url}/api/statistics')
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict), "Statistics should return object"
        
        # Sprawdź kluczowe pola statystyk
        expected_fields = ['total_signals', 'win_rate']
        for field in expected_fields:
            assert field in data, f"Statistics missing field: {field}"
        
        # Sprawdź typy danych
        if 'total_signals' in data:
            assert isinstance(data['total_signals'], int), "total_signals should be integer"
        
        if 'win_rate' in data:
            assert isinstance(data['win_rate'], (int, float)), "win_rate should be numeric"
    
    def test_pnl_chart_endpoint(self, base_url):
        """Test endpoint wykresu PnL."""
        response = requests.get(f'{base_url}/api/pnl-chart')
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list), "PnL Chart should return array"
        
        if data:
            item = data[0]
            expected_fields = ['date', 'cumulative_pnl']
            for field in expected_fields:
                assert field in item, f"PnL Chart item missing field: {field}"
    
    def test_pnl_distribution_endpoint(self, base_url):
        """Test endpoint rozkładu PnL."""
        response = requests.get(f'{base_url}/api/pnl-distribution')
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list), "PnL Distribution should return array"
        
        if data:
            item = data[0]
            expected_fields = ['range_start', 'count']
            for field in expected_fields:
                assert field in item, f"PnL Distribution item missing field: {field}"
    
    def test_pairs_endpoint(self, base_url):
        """Test endpoint par tradingowych."""
        response = requests.get(f'{base_url}/api/pairs')
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list), "Pairs should return array"
        
        # Sprawdź czy pary są stringami
        for pair in data:
            assert isinstance(pair, str), f"Pair should be string, got {type(pair)}"
            assert len(pair) > 0, "Pair should not be empty"
    
    def test_chart_data_consistency(self, base_url):
        """Test spójności danych między endpoint."""
        # Pobierz dane z różnych endpoint
        stats_response = requests.get(f'{base_url}/api/statistics')
        signals_response = requests.get(f'{base_url}/api/signals?limit=100')
        pairs_response = requests.get(f'{base_url}/api/pairs')
        
        assert all(r.status_code == 200 for r in [stats_response, signals_response, pairs_response])
        
        stats = stats_response.json()
        signals = signals_response.json()
        pairs = pairs_response.json()
        
        # Sprawdź spójność liczby sygnałów
        if 'total_signals' in stats and signals:
            # Liczba sygnałów w statystykach powinna być >= liczby pobranych sygnałów
            assert stats['total_signals'] >= len(signals), "Statistics total_signals inconsistent"
        
        # Sprawdź czy wszystkie pary z sygnałów są w liście par
        signal_pairs = set(signal['pair'] for signal in signals if 'pair' in signal)
        for pair in signal_pairs:
            assert pair in pairs, f"Signal pair {pair} not found in pairs list"
    
    @pytest.mark.slow
    def test_chart_endpoints_performance(self, base_url, chart_endpoints):
        """Test wydajności endpoint wykresów."""
        import time
        
        for endpoint, name in chart_endpoints:
            start_time = time.time()
            response = requests.get(f'{base_url}{endpoint}')
            end_time = time.time()
            
            assert response.status_code == 200
            assert (end_time - start_time) < 10.0, f"{name} endpoint too slow: {end_time - start_time:.2f}s"


def test_all_chart_apis_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    print("🔍 TEST WSZYSTKICH API ENDPOINT DLA WYKRESÓW")
    print("=" * 50)
    
    endpoints = [
        ("/api/statistics", "Statistics"),
        ("/api/pnl-chart", "PnL Chart"),
        ("/api/pnl-distribution", "PnL Distribution"),
        ("/api/signals?limit=5", "Signals"),
        ("/api/pairs", "Pairs")
    ]
    
    for endpoint, name in endpoints:
        print(f"\n📊 {name} ({endpoint}):")
        try:
            response = requests.get(f'http://localhost:5000{endpoint}')
            if response.status_code == 200:
                data = response.json()
                
                if isinstance(data, list):
                    print(f"  ✅ Status: OK - Array with {len(data)} items")
                    if len(data) > 0:
                        print(f"  📋 First item keys: {list(data[0].keys())}")
                        if name == "PnL Chart" and len(data) > 0:
                            item = data[0]
                            print(f"  📈 Sample data: date={item.get('date')}, cumulative_pnl={item.get('cumulative_pnl')}")
                        elif name == "PnL Distribution" and len(data) > 0:
                            item = data[0]
                            print(f"  📊 Sample data: range_start={item.get('range_start')}, count={item.get('count')}")
                    else:
                        print(f"  ⚠️  Empty array")
                        
                elif isinstance(data, dict):
                    print(f"  ✅ Status: OK - Object")
                    print(f"  📋 Keys: {list(data.keys())}")
                    if name == "Statistics":
                        print(f"  📈 Sample stats: total_signals={data.get('total_signals')}, win_rate={data.get('win_rate')}")
                        if 'pair_stats' in data:
                            pair_stats = data['pair_stats']
                            print(f"  📊 Pair stats: {len(pair_stats) if pair_stats else 0} pairs")
                else:
                    print(f"  ✅ Status: OK - {type(data).__name__}")
                    
            else:
                print(f"  ❌ Error: {response.status_code}")
                print(f"  📄 Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"  ❌ Exception: {e}")
    
    print(f"\n🎯 PODSUMOWANIE:")
    print("Sprawdź w przeglądarce:")
    print("1. Otwórz konsolę deweloperską (F12)")
    print("2. Sprawdź czy są błędy JavaScript")
    print("3. Sprawdź czy logi 'Updating dashboard with data' są wyświetlane")
    print("4. Sprawdź czy logi 'Updating PnL Distribution chart' są wyświetlane")


if __name__ == "__main__":
    test_all_chart_apis_manual()
