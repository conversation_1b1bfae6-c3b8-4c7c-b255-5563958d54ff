<!DOCTYPE html>
<html lang="pl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🔧 Admin Panel - Discord Bybit Signal Monitor</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
      :root {
        --admin-primary: #2c3e50;
        --admin-secondary: #34495e;
        --admin-accent: #3498db;
        --admin-success: #27ae60;
        --admin-warning: #f39c12;
        --admin-danger: #e74c3c;
        --admin-dark: #1a252f;
      }

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .admin-navbar {
        background: var(--admin-dark) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .admin-navbar .navbar-brand {
        font-weight: bold;
        color: #fff !important;
      }

      .admin-sidebar {
        background: var(--admin-primary);
        min-height: calc(100vh - 76px);
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
      }

      .admin-sidebar .nav-link {
        color: #bdc3c7;
        padding: 12px 20px;
        border-radius: 8px;
        margin: 4px 8px;
        transition: all 0.3s ease;
      }

      .admin-sidebar .nav-link:hover,
      .admin-sidebar .nav-link.active {
        background: var(--admin-accent);
        color: white;
        transform: translateX(5px);
      }

      .admin-sidebar .nav-link i {
        width: 20px;
        margin-right: 10px;
      }

      .admin-content {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        margin: 20px;
        padding: 30px;
      }

      .stat-card {
        background: linear-gradient(
          135deg,
          var(--admin-accent) 0%,
          #5dade2 100%
        );
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .stat-card:hover {
        transform: translateY(-5px);
      }

      .stat-card.success {
        background: linear-gradient(
          135deg,
          var(--admin-success) 0%,
          #58d68d 100%
        );
      }

      .stat-card.warning {
        background: linear-gradient(
          135deg,
          var(--admin-warning) 0%,
          #f7dc6f 100%
        );
      }

      .stat-card.danger {
        background: linear-gradient(
          135deg,
          var(--admin-danger) 0%,
          #ec7063 100%
        );
      }

      .stat-card .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
      }

      .stat-card .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
      }

      .stat-card .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: none;
        margin-bottom: 20px;
      }

      .admin-card .card-header {
        background: linear-gradient(
          135deg,
          var(--admin-primary) 0%,
          var(--admin-secondary) 100%
        );
        color: white;
        border-radius: 15px 15px 0 0 !important;
        border: none;
        padding: 20px;
      }

      .admin-card .card-header h5 {
        margin: 0;
        font-weight: 600;
      }

      .btn-admin {
        background: linear-gradient(
          135deg,
          var(--admin-accent) 0%,
          #5dade2 100%
        );
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .btn-admin:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        color: white;
      }

      .btn-admin-success {
        background: linear-gradient(
          135deg,
          var(--admin-success) 0%,
          #58d68d 100%
        );
      }

      .btn-admin-warning {
        background: linear-gradient(
          135deg,
          var(--admin-warning) 0%,
          #f7dc6f 100%
        );
      }

      .btn-admin-danger {
        background: linear-gradient(
          135deg,
          var(--admin-danger) 0%,
          #ec7063 100%
        );
      }

      .quick-actions {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      }

      .quick-actions .action-btn {
        display: block;
        width: 100%;
        margin-bottom: 15px;
        padding: 15px;
        text-align: left;
        border-radius: 10px;
        text-decoration: none;
        transition: all 0.3s ease;
      }

      .quick-actions .action-btn:hover {
        transform: translateX(10px);
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-active {
        background: var(--admin-success);
        animation: pulse 2s infinite;
      }

      .status-inactive {
        background: var(--admin-danger);
      }

      @keyframes pulse {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
        100% {
          opacity: 1;
        }
      }

      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .fade-in {
        animation: fadeIn 0.5s ease-in;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
      <div class="container-fluid">
        <a class="navbar-brand" href="/">
          <i class="fas fa-cogs"></i>
          Admin Panel - Discord Bybit Monitor
        </a>
        <div class="navbar-nav ms-auto">
          <a class="nav-link text-light" href="/" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Dashboard
          </a>
          <a class="nav-link text-light" href="/api/health">
            <i class="fas fa-heartbeat"></i>
            Health
          </a>
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 p-0">
          <div class="admin-sidebar">
            <nav class="nav flex-column py-3">
              <a class="nav-link active" href="/">
                <i class="fas fa-home"></i>
                Dashboard
              </a>
              <a class="nav-link" href="/strategies">
                <i class="fas fa-chart-line"></i>
                Strategie
              </a>
              <a class="nav-link" href="/channels">
                <i class="fab fa-discord"></i>
                Kanały Discord
              </a>
              <a class="nav-link" href="/config">
                <i class="fas fa-cog"></i>
                Konfiguracja
              </a>
              <a class="nav-link" href="/logs">
                <i class="fas fa-list-alt"></i>
                Logi
              </a>
              <hr class="text-light mx-3" />
              <a class="nav-link" href="http://localhost:5000" target="_blank">
                <i class="fas fa-chart-bar"></i>
                Trading Dashboard
              </a>
            </nav>
          </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-10">
          <div class="admin-content fade-in">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h1 class="h2 mb-0">
                <i class="fas fa-tachometer-alt text-primary"></i>
                Dashboard Administratora
              </h1>
              <div class="d-flex gap-2">
                <button class="btn btn-admin" onclick="refreshStats()">
                  <i class="fas fa-sync-alt"></i>
                  Odśwież
                </button>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="stat-card">
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <div class="stat-number" id="totalStrategies">
                        {{ stats.total_strategies or 0 }}
                      </div>
                      <div class="stat-label">Strategie</div>
                      <small>
                        <span class="status-indicator status-active"></span>
                        {{ stats.active_strategies or 0 }} aktywnych
                      </small>
                    </div>
                    <div class="stat-icon">
                      <i class="fas fa-chart-line"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card success">
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <div class="stat-number" id="totalChannels">
                        {{ stats.total_channels or 0 }}
                      </div>
                      <div class="stat-label">Kanały Discord</div>
                      <small>
                        <span class="status-indicator status-active"></span>
                        {{ stats.active_channels or 0 }} aktywnych
                      </small>
                    </div>
                    <div class="stat-icon">
                      <i class="fab fa-discord"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card warning">
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <div class="stat-number" id="totalConfigs">
                        {{ stats.total_configs or 0 }}
                      </div>
                      <div class="stat-label">Konfiguracje</div>
                      <small>
                        <span class="status-indicator status-active"></span>
                        Systemowe
                      </small>
                    </div>
                    <div class="stat-icon">
                      <i class="fas fa-cog"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card danger">
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <div class="stat-number" id="recentActions">
                        {{ stats.recent_actions or 0 }}
                      </div>
                      <div class="stat-label">Ostatnie akcje</div>
                      <small>
                        <span class="status-indicator status-active"></span>
                        Ostatnie 24h
                      </small>
                    </div>
                    <div class="stat-icon">
                      <i class="fas fa-history"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
              <div class="col-md-8">
                <div class="admin-card">
                  <div class="card-header">
                    <h5>
                      <i class="fas fa-bolt"></i>
                      Szybkie akcje
                    </h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <a href="/strategies" class="action-btn btn-admin">
                          <i class="fas fa-plus-circle me-2"></i>
                          Dodaj nową strategię
                        </a>
                        <a
                          href="/channels"
                          class="action-btn btn-admin-success"
                        >
                          <i class="fab fa-discord me-2"></i>
                          Zarządzaj kanałami Discord
                        </a>
                      </div>
                      <div class="col-md-6">
                        <a href="/config" class="action-btn btn-admin-warning">
                          <i class="fas fa-cog me-2"></i>
                          Konfiguracja systemu
                        </a>
                        <a href="/logs" class="action-btn btn-admin-danger">
                          <i class="fas fa-list-alt me-2"></i>
                          Przejrzyj logi
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="admin-card">
                  <div class="card-header">
                    <h5>
                      <i class="fas fa-info-circle"></i>
                      Status systemu
                    </h5>
                  </div>
                  <div class="card-body">
                    <div
                      class="d-flex justify-content-between align-items-center mb-3"
                    >
                      <span>Baza danych</span>
                      <span class="badge bg-success">
                        <i class="fas fa-check"></i> Połączona
                      </span>
                    </div>
                    <div
                      class="d-flex justify-content-between align-items-center mb-3"
                    >
                      <span>API Bybit</span>
                      <span class="badge bg-warning" id="bybitStatus">
                        <i class="fas fa-question"></i> Sprawdzanie...
                      </span>
                    </div>
                    <div
                      class="d-flex justify-content-between align-items-center mb-3"
                    >
                      <span>Discord Bot</span>
                      <span class="badge bg-info" id="discordStatus">
                        <i class="fas fa-question"></i> Sprawdzanie...
                      </span>
                    </div>
                    <hr />
                    <small class="text-muted">
                      <i class="fas fa-clock"></i>
                      Ostatnia aktualizacja:
                      <span id="lastUpdate">--:--:--</span>
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      // Funkcje JavaScript dla panelu administratora

      function refreshStats() {
        // Dodaj spinner do przycisku
        const btn = event.target.closest("button");
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<span class="loading-spinner"></span> Odświeżanie...';
        btn.disabled = true;

        // Symulacja odświeżania (w rzeczywistości wywołałbyś API)
        setTimeout(() => {
          btn.innerHTML = originalContent;
          btn.disabled = false;
          updateLastUpdate();

          // Tutaj dodałbyś rzeczywiste wywołanie API
          // loadStats();
        }, 1500);
      }

      function updateLastUpdate() {
        const now = new Date();
        document.getElementById("lastUpdate").textContent =
          now.toLocaleTimeString("pl-PL");
      }

      function checkSystemStatus() {
        // Sprawdź status API Bybit
        fetch("/api/health")
          .then((response) => response.json())
          .then((data) => {
            const bybitStatus = document.getElementById("bybitStatus");
            const discordStatus = document.getElementById("discordStatus");

            if (data.status === "healthy") {
              bybitStatus.className = "badge bg-success";
              bybitStatus.innerHTML = '<i class="fas fa-check"></i> Połączone';

              discordStatus.className = "badge bg-success";
              discordStatus.innerHTML = '<i class="fas fa-check"></i> Aktywny';
            } else {
              bybitStatus.className = "badge bg-danger";
              bybitStatus.innerHTML = '<i class="fas fa-times"></i> Błąd';

              discordStatus.className = "badge bg-danger";
              discordStatus.innerHTML = '<i class="fas fa-times"></i> Błąd';
            }
          })
          .catch((error) => {
            console.error("Error checking system status:", error);
          });
      }

      // Inicjalizacja
      document.addEventListener("DOMContentLoaded", function () {
        updateLastUpdate();
        checkSystemStatus();

        // Sprawdzaj status co 30 sekund
        setInterval(checkSystemStatus, 30000);
      });
    </script>
  </body>
</html>
