#!/usr/bin/env python3
"""
Database Manager for Admin Panel
================================

Zarządza strukturą bazy danych dla panelu administratora.
Rozszerza istniejącą bazę danych o nowe tabele dla strategii,
kanałów Discord i konfiguracji systemowej.

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
"""

import sqlite3
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    🗄️ Database Manager dla panelu administratora
    
    Zarządza strukturą bazy danych i migracjami dla:
    - Strategii tradingowych
    - Kanałów Discord
    - Konfiguracji systemowej
    - Whitelisty botów
    """
    
    def __init__(self, db_path: str = 'signals.db'):
        """
        Inicjalizacja Database Manager.
        
        Args:
            db_path: Ścieżka do pliku bazy danych SQLite
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._ensure_admin_tables()
    
    @contextmanager
    def get_connection(self):
        """Context manager dla połączeń z bazą danych."""
        conn = sqlite3.connect(
            self.db_path, 
            detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES
        )
        conn.row_factory = sqlite3.Row  # Umożliwia dostęp do kolumn po nazwie
        try:
            yield conn
        finally:
            conn.close()
    
    def _ensure_admin_tables(self):
        """Tworzy tabele dla panelu administratora jeśli nie istnieją."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Tabela strategii
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    timeframe_min INTEGER DEFAULT 60,
                    risk_percent REAL DEFAULT 2.0,
                    max_signals_per_day INTEGER DEFAULT 10,
                    signal_validity_hours INTEGER DEFAULT 48,
                    min_timeframe_minutes INTEGER DEFAULT 15,
                    default_timeframe_multiplier INTEGER DEFAULT 60,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela kanałów Discord
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS discord_channels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id TEXT UNIQUE NOT NULL,
                    channel_name TEXT NOT NULL,
                    guild_id TEXT,
                    guild_name TEXT,
                    strategy_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    allow_bot_messages BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategies (id)
                )
            ''')
            
            # Tabela whitelisty botów
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS bot_whitelist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id INTEGER NOT NULL,
                    bot_id TEXT NOT NULL,
                    bot_name TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_id, bot_id),
                    FOREIGN KEY (channel_id) REFERENCES discord_channels (id)
                )
            ''')
            
            # Tabela konfiguracji systemowej
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    config_type TEXT DEFAULT 'string',
                    is_sensitive BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela logów administratora
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action TEXT NOT NULL,
                    entity_type TEXT NOT NULL,
                    entity_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    user_ip TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            self.logger.info("✅ Admin panel database tables ensured")
            
            # Dodaj domyślną konfigurację jeśli nie istnieje
            self._insert_default_config(conn)
    
    def _insert_default_config(self, conn: sqlite3.Connection):
        """Wstawia domyślną konfigurację systemową."""
        cursor = conn.cursor()
        
        default_configs = [
            ('bybit_api_key', '', 'Klucz API Bybit', 'string', True),
            ('bybit_api_secret', '', 'Sekret API Bybit', 'string', True),
            ('price_check_interval', '60', 'Interwał sprawdzania cen (sekundy)', 'integer', False),
            ('dashboard_url', 'http://localhost:5000', 'URL dashboard', 'string', False),
            ('cache_timeout', '300', 'Timeout cache (sekundy)', 'integer', False),
            ('max_requests_per_minute', '100', 'Maksymalna liczba requestów na minutę', 'integer', False),
            ('enable_notifications', 'true', 'Włącz powiadomienia', 'boolean', False),
            ('log_level', 'INFO', 'Poziom logowania', 'string', False),
        ]
        
        for key, value, description, config_type, is_sensitive in default_configs:
            cursor.execute('''
                INSERT OR IGNORE INTO system_config 
                (key, value, description, config_type, is_sensitive)
                VALUES (?, ?, ?, ?, ?)
            ''', (key, value, description, config_type, is_sensitive))
        
        conn.commit()
        self.logger.info("✅ Default system configuration inserted")
    
    def log_admin_action(self, action: str, entity_type: str, entity_id: Optional[int] = None,
                        old_values: Optional[Dict] = None, new_values: Optional[Dict] = None,
                        user_ip: Optional[str] = None):
        """
        Loguje akcję administratora.
        
        Args:
            action: Typ akcji (CREATE, UPDATE, DELETE)
            entity_type: Typ encji (strategy, channel, config)
            entity_id: ID encji
            old_values: Stare wartości (dla UPDATE/DELETE)
            new_values: Nowe wartości (dla CREATE/UPDATE)
            user_ip: IP użytkownika
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO admin_logs 
                (action, entity_type, entity_id, old_values, new_values, user_ip)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                action, entity_type, entity_id,
                str(old_values) if old_values else None,
                str(new_values) if new_values else None,
                user_ip
            ))
            conn.commit()
    
    def get_admin_logs(self, limit: int = 100, entity_type: Optional[str] = None) -> List[Dict]:
        """
        Pobiera logi administratora.
        
        Args:
            limit: Maksymalna liczba logów
            entity_type: Filtr typu encji
            
        Returns:
            Lista logów administratora
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM admin_logs"
            params = []
            
            if entity_type:
                query += " WHERE entity_type = ?"
                params.append(entity_type)
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def migrate_existing_config(self):
        """
        Migruje istniejącą konfigurację ze zmiennych środowiskowych do bazy danych.
        """
        env_mappings = {
            'BYBIT_API_KEY': 'bybit_api_key',
            'BYBIT_API_SECRET': 'bybit_api_secret',
            'PRICE_CHECK_INTERVAL_SEC': 'price_check_interval',
            'DASHBOARD_URL': 'dashboard_url',
            'CACHE_TIMEOUT': 'cache_timeout',
            'MAX_REQUESTS_PER_MINUTE': 'max_requests_per_minute',
        }
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            for env_key, config_key in env_mappings.items():
                env_value = os.getenv(env_key)
                if env_value:
                    cursor.execute('''
                        UPDATE system_config 
                        SET value = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE key = ? AND (value = '' OR value IS NULL)
                    ''', (env_value, config_key))
            
            conn.commit()
            self.logger.info("✅ Existing configuration migrated to database")
