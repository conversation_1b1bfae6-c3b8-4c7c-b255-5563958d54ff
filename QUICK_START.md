# 🚀 Quick Start Guide - Discord Bybit Signal Monitor

## 📋 Wymagania wstępne

- Python 3.8+
- Discord Bot Token
- Bybit API Keys (opcjonalne)
- Dostęp do kanałów Discord

## ⚡ <PERSON><PERSON>b<PERSON> start (5 minut)

### 1. Sklonuj i przygotuj środowisko

```bash
# Sklonuj repozytorium
git clone <repository-url>
cd discord-bybit-signal-monitor

# Zainstaluj zależ<PERSON>ci
pip install -r requirements.txt
```

### 2. Konfiguracja podstawowa

Utwórz plik `.env`:

```env
# Discord Configuration
DISCORD_TOKEN=your_discord_bot_token_here
CHANNEL_ID=your_discord_channel_id_here
GUILD_ID=your_discord_guild_id_here

# Bybit API (opcjonalne)
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# Bot Configuration
ALLOW_BOT_MESSAGES=true
BOT_WHITELIST=bot_id_1,bot_id_2

# System Configuration
SIGNAL_VALIDITY_HOURS=48
PRICE_CHECK_INTERVAL_SEC=60
```

### 3. Uruchom system

#### Opcja A: Automatyczne uruchomienie (Windows)
```bash
start_system.bat
```

#### Opcja B: Python script
```bash
python start_servers.py
```

#### Opcja C: Ręczne uruchomienie
```bash
# Terminal 1: Trading Dashboard
python dashboard.py

# Terminal 2: Admin Panel
python admin_panel.py

# Terminal 3: Discord Monitor (opcjonalnie)
python discord_bybit_signal_monitor.py
```

### 4. Otwórz w przeglądarce

- **Trading Dashboard**: http://localhost:5000
- **Admin Panel**: http://localhost:5001

## 🔧 Konfiguracja przez Panel Administratora

### 1. Dodaj strategię

1. Otwórz http://localhost:5001
2. Kliknij "Strategie" → "Dodaj strategię"
3. Wypełnij formularz:
   - **Nazwa**: np. "Scalping Strategy"
   - **Timeframe**: 60 minut
   - **Ryzyko**: 2.0%
   - **Max sygnałów/dzień**: 10
   - **Ważność sygnału**: 48 godzin

### 2. Dodaj kanał Discord

1. Kliknij "Kanały Discord" → "Dodaj kanał"
2. Wypełnij formularz:
   - **Discord Channel ID**: ID kanału Discord
   - **Nazwa kanału**: Czytelna nazwa
   - **Strategia**: Wybierz utworzoną strategię
   - **Zezwalaj na boty**: ✓ (jeśli potrzebne)

### 3. Dodaj boty do whitelisty (opcjonalnie)

1. W karcie kanału kliknij "Dodaj bota"
2. Wprowadź Discord ID bota
3. Dodaj nazwę bota (opcjonalnie)

### 4. Konfiguracja systemowa

1. Kliknij "Konfiguracja"
2. Dodaj/edytuj konfiguracje:
   - `bybit_api_key` - Klucz API Bybit
   - `bybit_api_secret` - Sekret API Bybit
   - `price_check_interval` - Interwał sprawdzania cen

## 📊 Monitorowanie

### Trading Dashboard (http://localhost:5000)

- **Przegląd sygnałów** - Wszystkie sygnały w czasie rzeczywistym
- **Statystyki** - Wydajność, Sharpe ratio, drawdown
- **Wykresy** - Wizualizacja wyników
- **Filtry** - Filtrowanie po statusie, parze, strategii
- **Eksport** - CSV, JSON

### Admin Panel (http://localhost:5001)

- **Dashboard** - Przegląd systemu
- **Strategie** - Zarządzanie strategiami
- **Kanały** - Konfiguracja kanałów Discord
- **Konfiguracja** - Ustawienia systemowe
- **Logi** - Historia akcji administratora

## 🔍 Testowanie

### Uruchom testy

```bash
# Wszystkie testy
python -m pytest

# Testy panelu administratora
python -m pytest tests/unit/test_admin_panel.py -v

# Testy z pokryciem
python -m pytest --cov=models --cov=admin_panel
```

### Test API

```bash
# Health check
curl http://localhost:5001/api/health

# Lista strategii
curl http://localhost:5001/api/strategies

# Lista kanałów
curl http://localhost:5001/api/channels
```

## 🐛 Rozwiązywanie problemów

### Problem: Błędy Unicode w logach (Windows)
**Rozwiązanie**: To normalne w Windows - aplikacja działa poprawnie

### Problem: Brak dostępu do panelu administratora
**Rozwiązanie**: 
1. Sprawdź czy port 5001 jest wolny
2. Sprawdź firewall
3. Uruchom jako administrator

### Problem: Discord bot nie odpowiada
**Rozwiązanie**:
1. Sprawdź token Discord w `.env`
2. Sprawdź uprawnienia bota
3. Sprawdź ID kanału

### Problem: Brak sygnałów
**Rozwiązanie**:
1. Sprawdź konfigurację kanałów w panelu administratora
2. Sprawdź whitelistę botów
3. Sprawdź logi w `discord_monitor.log`

## 📚 Dalsze kroki

1. **Konfiguracja produkcyjna**:
   - Użyj prawdziwych kluczy API Bybit
   - Skonfiguruj HTTPS
   - Użyj production WSGI server

2. **Rozszerzenia**:
   - Dodaj więcej strategii
   - Skonfiguruj powiadomienia
   - Zintegruj z dodatkowymi giełdami

3. **Monitoring**:
   - Skonfiguruj alerty
   - Monitoruj wydajność
   - Regularnie sprawdzaj logi

## 🆘 Pomoc

- **Dokumentacja**: README.md
- **Logi**: Sprawdź pliki `.log`
- **API**: http://localhost:5001/api/health
- **Status**: Panel administratora → Dashboard

---

**Powodzenia w tradingu! 🚀📈**
