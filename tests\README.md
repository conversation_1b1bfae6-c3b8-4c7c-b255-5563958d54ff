# Discord Bybit Signal Monitor - Test Suite

Comprehensive test suite for the Discord Bybit Signal Monitor application.

## 📁 Test Structure

```
tests/
├── __init__.py                     # Test package initialization
├── conftest.py                     # Shared pytest fixtures and configuration
├── README.md                       # This file
├── unit/                          # Unit tests for individual components
│   ├── __init__.py
│   ├── test_signal_parsing.py     # Tests for signal parsing logic
│   └── test_data_processing.py    # Tests for data processing functions
├── integration/                   # Integration tests for system components
│   ├── __init__.py
│   ├── test_dashboard_endpoints.py # Complete dashboard endpoint tests
│   ├── test_database.py           # Database integration tests
│   └── test_signal_workflow.py    # End-to-end signal processing tests
├── api/                          # API endpoint tests
│   ├── __init__.py
│   ├── test_signals_api.py        # Signals API endpoint tests
│   ├── test_statistics_api.py     # Statistics API endpoint tests
│   └── test_charts_api.py         # Charts API endpoint tests
└── debug/                        # Debug and troubleshooting tests
    ├── __init__.py
    ├── test_api_direct.py         # Direct API inspection and debugging
    ├── test_pnl_issues.py         # PnL calculation debugging
    └── test_dataframe_fixes.py    # DataFrame handling debugging
```

## 🚀 Running Tests

### Using Make (Recommended)

```bash
# Run all tests
make test

# Run tests with verbose output
make test-verbose

# Run specific test categories
make test-unit           # Unit tests only
make test-integration    # Integration tests only
make test-api           # API tests only
make test-debug         # Debug tests only

# Run performance tests
make test-quick         # Quick tests (excluding slow)
make test-slow          # Slow tests only

# Run with coverage
make test-coverage      # Generate coverage report
```

### Using Pytest Directly

```bash
# Run all tests
pytest tests/

# Run with verbose output
pytest tests/ -v

# Run specific test files
pytest tests/api/test_signals_api.py -v
pytest tests/debug/test_pnl_issues.py -v

# Run tests by marker
pytest tests/ -m "unit" -v          # Unit tests only
pytest tests/ -m "api" -v           # API tests only
pytest tests/ -m "not slow" -v      # Exclude slow tests

# Run with coverage
pytest tests/ --cov=. --cov-report=html
```

### Legacy Manual Testing

```bash
# Run individual test files manually (backward compatibility)
python tests/api/test_signals_api.py
python tests/debug/test_pnl_issues.py
python tests/integration/test_dashboard_endpoints.py
```

## 🏷️ Test Markers

Tests are organized using pytest markers:

- `@pytest.mark.unit` - Unit tests for individual components
- `@pytest.mark.integration` - Integration tests for system components
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.debug` - Debug and troubleshooting tests
- `@pytest.mark.slow` - Tests that take longer to run
- `@pytest.mark.database` - Tests that require database access
- `@pytest.mark.network` - Tests that require network access

## 🔧 Test Configuration

### Fixtures

The `conftest.py` file provides shared fixtures:

- `test_db` - Temporary test database with sample data
- `mock_bybit_api` - Mock Bybit API responses
- `base_url` - Base URL for API tests
- `sample_signal_data` - Sample signal data for testing
- `sample_discord_message` - Sample Discord message for parsing tests

### Configuration

Test configuration is managed through:

- `pytest.ini` - Main pytest configuration
- `conftest.py` - Shared fixtures and test setup
- Environment variables for test-specific settings

## 📊 Test Categories

### Unit Tests (`tests/unit/`)

Test individual functions and components in isolation:
- Signal parsing logic
- Data processing functions
- Utility functions
- Business logic components

### Integration Tests (`tests/integration/`)

Test system components working together:
- Database interactions
- Dashboard functionality
- Complete workflows
- Component integration

### API Tests (`tests/api/`)

Test REST API endpoints:
- Signals API endpoints
- Statistics API endpoints
- Charts API endpoints
- Response validation
- Error handling

### Debug Tests (`tests/debug/`)

Debug and troubleshooting tests:
- Direct API inspection
- PnL calculation debugging
- DataFrame handling issues
- Data consistency checks

## 🎯 Test Examples

### Running Specific Tests

```bash
# Test only signals API
pytest tests/api/test_signals_api.py::TestSignalsAPI::test_signals_api_basic -v

# Test PnL calculation issues
pytest tests/debug/test_pnl_issues.py::TestPnLIssuesDebug::test_pnl_consistency -v

# Test dashboard endpoints
pytest tests/integration/test_dashboard_endpoints.py -v
```

### Running Tests with Filters

```bash
# Run only fast tests
pytest tests/ -m "not slow" -v

# Run only database tests
pytest tests/ -m "database" -v

# Run API and integration tests
pytest tests/ -m "api or integration" -v
```

## 🔍 Debugging Failed Tests

### Verbose Output

```bash
# Run with maximum verbosity
pytest tests/ -vv --tb=long

# Show local variables in tracebacks
pytest tests/ --tb=long --showlocals
```

### Debug Specific Issues

```bash
# Debug PnL calculation issues
python tests/debug/test_pnl_issues.py

# Debug API responses
python tests/debug/test_api_direct.py

# Debug DataFrame handling
python tests/debug/test_dataframe_fixes.py
```

## 📈 Coverage Reports

Generate test coverage reports:

```bash
# HTML coverage report
pytest tests/ --cov=. --cov-report=html
# Open htmlcov/index.html in browser

# Terminal coverage report
pytest tests/ --cov=. --cov-report=term-missing

# XML coverage report (for CI/CD)
pytest tests/ --cov=. --cov-report=xml
```

## 🚨 Prerequisites

### Required Dependencies

```bash
pip install pytest requests pandas sqlite3
```

### Optional Dependencies

```bash
pip install pytest-cov pytest-mock pytest-timeout
```

### Test Environment

- Dashboard should be running on `http://localhost:5000`
- Test database will be created automatically
- No external API keys required for most tests

## 📝 Writing New Tests

### Test File Naming

- Unit tests: `test_<component>.py`
- Integration tests: `test_<feature>_integration.py`
- API tests: `test_<endpoint>_api.py`
- Debug tests: `test_<issue>_debug.py`

### Test Class Naming

```python
class TestSignalParsing:      # Unit test
class TestDashboardAPI:       # API test
class TestSignalWorkflow:     # Integration test
class TestPnLDebug:          # Debug test
```

### Using Fixtures

```python
def test_signal_api(base_url, sample_signal_data):
    response = requests.get(f'{base_url}/api/signals')
    assert response.status_code == 200
```

### Adding Markers

```python
@pytest.mark.api
@pytest.mark.slow
def test_large_dataset_api(base_url):
    # Test implementation
    pass
```

## 🎉 Success Criteria

Tests should verify:

- ✅ All API endpoints return correct status codes
- ✅ Data formats match expected schemas
- ✅ PnL calculations are accurate
- ✅ Signal status logic is correct
- ✅ Database operations work correctly
- ✅ Error handling is robust
- ✅ Performance meets requirements
