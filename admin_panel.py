#!/usr/bin/env python3
"""
🔧 ADMIN PANEL - Backend dla panelu administratora
Discord Bybit Signal Monitor - Professional Admin Interface

Zaawansowany panel administratora z pełną funkcjonalnością:
- Zarządzanie strategiami tradingowymi
- Zarządzanie kanałami Discord (jeden kanał = jedna strategia)
- Konfiguracja whitelisty botów per kanał
- Konfiguracja parametrów systemowych
- Logi administratora i audyt

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
Wersja: 1.0 Admin Panel Edition
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.exceptions import BadRequest
import json

# Import modeli
from models.database import DatabaseManager
from models.strategy import Strategy, StrategyManager
from models.channel import DiscordChannel, ChannelManager
from models.config import SystemConfig, ConfigManager

# Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('admin_panel.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Inicjalizacja Flask
app = Flask(__name__)
app.config.update({
    'SECRET_KEY': os.getenv('SECRET_KEY', 'admin-panel-secret-key-2025'),
    'JSON_SORT_KEYS': False
})

# Inicjalizacja managerów
DB_PATH = os.getenv('DB_PATH', 'signals.db')
db_manager = DatabaseManager(DB_PATH)
strategy_manager = StrategyManager(db_manager)
channel_manager = ChannelManager(db_manager)
config_manager = ConfigManager(db_manager)

logger.info("🚀 Admin Panel initialized successfully")


def get_client_ip() -> str:
    """Pobiera IP klienta dla logów."""
    return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)


def handle_api_error(func):
    """Dekorator do obsługi błędów API."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"API validation error: {e}")
            return jsonify({'error': str(e)}), 400
        except Exception as e:
            logger.error(f"API error in {func.__name__}: {e}")
            return jsonify({'error': 'Internal server error'}), 500
    wrapper.__name__ = func.__name__
    return wrapper


# ===== MAIN ADMIN PANEL ROUTES =====

@app.route('/')
def admin_dashboard():
    """🏠 Główna strona panelu administratora."""
    logger.info("🏠 Admin dashboard requested")

    try:
        # Pobierz podstawowe statystyki
        strategies = strategy_manager.get_all_strategies()
        channels = channel_manager.get_all_channels()
        configs = config_manager.get_all_configs()
        recent_logs = db_manager.get_admin_logs(limit=10)

        stats = {
            'total_strategies': len(strategies),
            'active_strategies': len([s for s in strategies if s.is_active]),
            'total_channels': len(channels),
            'active_channels': len([c for c in channels if c.is_active]),
            'total_configs': len(configs),
            'recent_actions': len(recent_logs)
        }

        return render_template('admin_panel.html', stats=stats)

    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        flash(f'Błąd ładowania dashboard: {e}', 'error')
        return render_template('admin_panel.html', stats={})


# ===== STRATEGY MANAGEMENT ROUTES =====

@app.route('/strategies')
def strategies_page():
    """📈 Strona zarządzania strategiami."""
    logger.info("📈 Strategies page requested")
    return render_template('admin_strategies.html')


@app.route('/api/strategies', methods=['GET'])
@handle_api_error
def api_get_strategies():
    """API: Pobierz wszystkie strategie."""
    strategies = strategy_manager.get_all_strategies()
    return jsonify([strategy.to_dict() for strategy in strategies])


@app.route('/api/strategies', methods=['POST'])
@handle_api_error
def api_create_strategy():
    """API: Utwórz nową strategię."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")

    strategy = Strategy.from_dict(data)
    strategy_id = strategy_manager.create_strategy(strategy, get_client_ip())

    logger.info(f"✅ Created strategy: {strategy.name}")
    return jsonify({'id': strategy_id, 'message': 'Strategia utworzona pomyślnie'}), 201


@app.route('/api/strategies/<int:strategy_id>', methods=['GET'])
@handle_api_error
def api_get_strategy(strategy_id: int):
    """API: Pobierz strategię po ID."""
    strategy = strategy_manager.get_strategy(strategy_id)
    if not strategy:
        return jsonify({'error': 'Strategia nie znaleziona'}), 404

    # Dodaj statystyki strategii
    stats = strategy_manager.get_strategy_stats(strategy_id)
    strategy_dict = strategy.to_dict()
    strategy_dict['stats'] = stats

    return jsonify(strategy_dict)


@app.route('/api/strategies/<int:strategy_id>', methods=['PUT'])
@handle_api_error
def api_update_strategy(strategy_id: int):
    """API: Aktualizuj strategię."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")

    data['id'] = strategy_id
    strategy = Strategy.from_dict(data)

    success = strategy_manager.update_strategy(strategy, get_client_ip())
    if success:
        logger.info(f"✅ Updated strategy: {strategy.name}")
        return jsonify({'message': 'Strategia zaktualizowana pomyślnie'})
    else:
        return jsonify({'error': 'Błąd aktualizacji strategii'}), 500


@app.route('/api/strategies/<int:strategy_id>', methods=['DELETE'])
@handle_api_error
def api_delete_strategy(strategy_id: int):
    """API: Usuń strategię."""
    success = strategy_manager.delete_strategy(strategy_id, get_client_ip())
    if success:
        logger.info(f"✅ Deleted strategy ID: {strategy_id}")
        return jsonify({'message': 'Strategia usunięta pomyślnie'})
    else:
        return jsonify({'error': 'Błąd usuwania strategii'}), 500


# ===== CHANNEL MANAGEMENT ROUTES =====

@app.route('/channels')
def channels_page():
    """📺 Strona zarządzania kanałami Discord."""
    logger.info("📺 Channels page requested")
    return render_template('admin_channels.html')


@app.route('/api/channels', methods=['GET'])
@handle_api_error
def api_get_channels():
    """API: Pobierz wszystkie kanały."""
    channels = channel_manager.get_all_channels()
    return jsonify([channel.to_dict() for channel in channels])


@app.route('/api/channels', methods=['POST'])
@handle_api_error
def api_create_channel():
    """API: Utwórz nowy kanał."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")

    channel = DiscordChannel.from_dict(data)
    channel_id = channel_manager.create_channel(channel, get_client_ip())

    logger.info(f"✅ Created channel: {channel.channel_name}")
    return jsonify({'id': channel_id, 'message': 'Kanał utworzony pomyślnie'}), 201


@app.route('/api/channels/<int:channel_id>', methods=['GET'])
@handle_api_error
def api_get_channel(channel_id: int):
    """API: Pobierz kanał po ID."""
    channel = channel_manager.get_channel(channel_id)
    if not channel:
        return jsonify({'error': 'Kanał nie znaleziony'}), 404

    return jsonify(channel.to_dict())


@app.route('/api/channels/<int:channel_id>', methods=['PUT'])
@handle_api_error
def api_update_channel(channel_id: int):
    """API: Aktualizuj kanał."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")

    data['id'] = channel_id
    channel = DiscordChannel.from_dict(data)

    success = channel_manager.update_channel(channel, get_client_ip())
    if success:
        logger.info(f"✅ Updated channel: {channel.channel_name}")
        return jsonify({'message': 'Kanał zaktualizowany pomyślnie'})
    else:
        return jsonify({'error': 'Błąd aktualizacji kanału'}), 500


@app.route('/api/channels/<int:channel_id>', methods=['DELETE'])
@handle_api_error
def api_delete_channel(channel_id: int):
    """API: Usuń kanał."""
    success = channel_manager.delete_channel(channel_id, get_client_ip())
    if success:
        logger.info(f"✅ Deleted channel ID: {channel_id}")
        return jsonify({'message': 'Kanał usunięty pomyślnie'})
    else:
        return jsonify({'error': 'Błąd usuwania kanału'}), 500


# ===== BOT WHITELIST ROUTES =====

@app.route('/api/channels/<int:channel_id>/bots', methods=['POST'])
@handle_api_error
def api_add_bot_to_whitelist(channel_id: int):
    """API: Dodaj bota do whitelisty kanału."""
    data = request.get_json()
    if not data or 'bot_id' not in data:
        raise ValueError("Bot ID jest wymagane")

    bot_id = data['bot_id']
    bot_name = data.get('bot_name')

    whitelist_id = channel_manager.add_bot_to_whitelist(
        channel_id, bot_id, bot_name, get_client_ip()
    )

    logger.info(f"✅ Added bot {bot_id} to channel {channel_id} whitelist")
    return jsonify({'id': whitelist_id, 'message': 'Bot dodany do whitelisty'}), 201


@app.route('/api/bots/<int:whitelist_id>', methods=['DELETE'])
@handle_api_error
def api_remove_bot_from_whitelist(whitelist_id: int):
    """API: Usuń bota z whitelisty."""
    success = channel_manager.remove_bot_from_whitelist(whitelist_id, get_client_ip())
    if success:
        logger.info(f"✅ Removed bot from whitelist ID: {whitelist_id}")
        return jsonify({'message': 'Bot usunięty z whitelisty'})
    else:
        return jsonify({'error': 'Błąd usuwania bota z whitelisty'}), 500


@app.route('/api/channels/<int:channel_id>/test', methods=['POST'])
@handle_api_error
def api_test_channel_connection(channel_id: int):
    """API: Testuj połączenie z kanałem Discord."""
    import asyncio
    import discord
    import os
    from dotenv import load_dotenv

    load_dotenv()

    # Pobierz kanał z bazy danych
    channel = channel_manager.get_channel(channel_id)
    if not channel:
        return jsonify({'error': 'Kanał nie znaleziony'}), 404

    # Pobierz token Discord z konfiguracji
    discord_token = None
    try:
        config_value = config_manager.get_config_by_key('discord_token')
        if config_value:
            discord_token = config_value.value
        else:
            discord_token = os.getenv('DISCORD_TOKEN')
    except:
        discord_token = os.getenv('DISCORD_TOKEN')

    if not discord_token:
        return jsonify({
            'success': False,
            'error': 'Brak tokenu Discord',
            'details': 'Skonfiguruj discord_token w panelu konfiguracji lub w pliku .env',
            'bot_connected': False,
            'channel_accessible': False,
            'can_read_messages': False,
            'can_send_messages': False,
            'warnings': ['Brak tokenu Discord - skonfiguruj w panelu konfiguracji']
        }), 400

    async def test_discord_connection():
        """Testuje połączenie z Discord."""
        try:
            # Utwórz klienta Discord z minimalnymi uprawnieniami
            intents = discord.Intents.default()
            intents.message_content = True
            client = discord.Client(intents=intents)

            connection_result = {
                'success': False,
                'bot_connected': False,
                'channel_accessible': False,
                'can_read_messages': False,
                'can_send_messages': False,
                'bot_info': None,
                'channel_info': None,
                'guild_info': None,
                'error': None,
                'warnings': []
            }

            @client.event
            async def on_ready():
                try:
                    connection_result['bot_connected'] = True
                    connection_result['bot_info'] = {
                        'name': client.user.name,
                        'id': str(client.user.id),
                        'discriminator': client.user.discriminator
                    }

                    # Sprawdź dostęp do kanału
                    discord_channel = client.get_channel(int(channel.channel_id))
                    if discord_channel:
                        connection_result['channel_accessible'] = True
                        connection_result['channel_info'] = {
                            'name': discord_channel.name,
                            'id': str(discord_channel.id),
                            'type': str(discord_channel.type),
                            'guild_id': str(discord_channel.guild.id) if discord_channel.guild else None
                        }

                        # Sprawdź dostęp do guild
                        if discord_channel.guild:
                            connection_result['guild_info'] = {
                                'name': discord_channel.guild.name,
                                'id': str(discord_channel.guild.id),
                                'member_count': discord_channel.guild.member_count
                            }

                        # Sprawdź uprawnienia
                        permissions = discord_channel.permissions_for(discord_channel.guild.me)
                        connection_result['can_read_messages'] = permissions.read_messages
                        connection_result['can_send_messages'] = permissions.send_messages

                        # Sprawdź czy bot może odczytywać historię
                        if permissions.read_message_history:
                            try:
                                # Spróbuj pobrać ostatnią wiadomość
                                async for message in discord_channel.history(limit=1):
                                    connection_result['warnings'].append(f'Ostatnia wiadomość: {message.created_at.strftime("%Y-%m-%d %H:%M:%S")}')
                                    break
                            except discord.Forbidden:
                                connection_result['warnings'].append('Brak dostępu do historii wiadomości')
                            except Exception as e:
                                connection_result['warnings'].append(f'Błąd odczytu historii: {str(e)}')
                        else:
                            connection_result['warnings'].append('Brak uprawnień do odczytu historii wiadomości')

                        # Sprawdź dodatkowe uprawnienia
                        if not permissions.read_messages:
                            connection_result['warnings'].append('Brak uprawnień do odczytu wiadomości')
                        if not permissions.send_messages:
                            connection_result['warnings'].append('Brak uprawnień do wysyłania wiadomości')
                        if not permissions.view_channel:
                            connection_result['warnings'].append('Brak uprawnień do przeglądania kanału')

                        connection_result['success'] = True
                    else:
                        connection_result['error'] = f'Nie można uzyskać dostępu do kanału {channel.channel_id}'
                        connection_result['warnings'].append('Sprawdź czy bot jest na serwerze i ma odpowiednie uprawnienia')

                except Exception as e:
                    connection_result['error'] = f'Błąd podczas testowania: {str(e)}'
                finally:
                    await client.close()

            # Uruchom klienta z timeoutem
            try:
                await asyncio.wait_for(client.start(discord_token), timeout=10.0)
            except asyncio.TimeoutError:
                connection_result['error'] = 'Timeout połączenia z Discord'
            except discord.LoginFailure:
                connection_result['error'] = 'Nieprawidłowy token Discord'
            except Exception as e:
                connection_result['error'] = f'Błąd połączenia: {str(e)}'

            return connection_result

        except Exception as e:
            return {
                'success': False,
                'error': f'Błąd testowania połączenia: {str(e)}'
            }

    # Sprawdź czy token wygląda na prawidłowy
    if discord_token and len(discord_token) < 50:
        return jsonify({
            'success': False,
            'error': 'Token Discord wydaje się nieprawidłowy',
            'details': f'Token ma tylko {len(discord_token)} znaków, powinien mieć około 70 znaków',
            'bot_connected': False,
            'channel_accessible': False,
            'can_read_messages': False,
            'can_send_messages': False,
            'warnings': ['Token Discord jest za krótki - sprawdź konfigurację']
        }), 400

    # Uruchom test w nowej pętli zdarzeń
    try:
        # Sprawdź czy jest aktywna pętla zdarzeń
        try:
            loop = asyncio.get_running_loop()
            # Jeśli jest, uruchom w nowym wątku
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, test_discord_connection())
                result = future.result(timeout=15)
        except RuntimeError:
            # Brak aktywnej pętli, uruchom normalnie
            result = asyncio.run(test_discord_connection())

        logger.info(f"🔍 Tested connection to channel {channel.channel_name}: {'✅' if result['success'] else '❌'}")
        return jsonify(result), 200

    except Exception as e:
        logger.error(f"❌ Error testing channel connection: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Błąd podczas testowania: {str(e)}',
            'bot_connected': False,
            'channel_accessible': False,
            'can_read_messages': False,
            'can_send_messages': False,
            'warnings': [f'Błąd techniczny: {str(e)}']
        }), 500


# ===== CONFIGURATION ROUTES =====

@app.route('/config')
def config_page():
    """⚙️ Strona konfiguracji systemowej."""
    logger.info("⚙️ Config page requested")
    return render_template('admin_config.html')


@app.route('/api/config', methods=['GET'])
@handle_api_error
def api_get_configs():
    """API: Pobierz wszystkie konfiguracje."""
    include_sensitive = request.args.get('include_sensitive', 'false').lower() == 'true'
    configs = config_manager.get_all_configs(include_sensitive)

    # Maskuj wrażliwe dane jeśli nie są jawnie żądane
    if not include_sensitive:
        for config in configs:
            if config.is_sensitive:
                config.value = config.get_display_value()

    return jsonify([config.to_dict() for config in configs])


@app.route('/api/config', methods=['POST'])
@handle_api_error
def api_create_config():
    """API: Utwórz nową konfigurację."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")

    config = SystemConfig.from_dict(data)
    config_id = config_manager.create_config(config, get_client_ip())

    logger.info(f"✅ Created config: {config.key}")
    return jsonify({'id': config_id, 'message': 'Konfiguracja utworzona pomyślnie'}), 201


@app.route('/api/config/<int:config_id>', methods=['PUT'])
@handle_api_error
def api_update_config(config_id: int):
    """API: Aktualizuj konfigurację."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")

    data['id'] = config_id
    config = SystemConfig.from_dict(data)

    success = config_manager.update_config(config, get_client_ip())
    if success:
        logger.info(f"✅ Updated config: {config.key}")
        return jsonify({'message': 'Konfiguracja zaktualizowana pomyślnie'})
    else:
        return jsonify({'error': 'Błąd aktualizacji konfiguracji'}), 500


@app.route('/api/config/<int:config_id>', methods=['DELETE'])
@handle_api_error
def api_delete_config(config_id: int):
    """API: Usuń konfigurację."""
    success = config_manager.delete_config(config_id, get_client_ip())
    if success:
        logger.info(f"✅ Deleted config ID: {config_id}")
        return jsonify({'message': 'Konfiguracja usunięta pomyślnie'})
    else:
        return jsonify({'error': 'Błąd usuwania konfiguracji'}), 500


# ===== LOGS AND MONITORING =====

@app.route('/logs')
def logs_page():
    """📋 Strona logów administratora."""
    logger.info("📋 Logs page requested")
    return render_template('admin_logs.html')


@app.route('/api/logs', methods=['GET'])
@handle_api_error
def api_get_logs():
    """API: Pobierz logi administratora."""
    limit = request.args.get('limit', 100, type=int)
    entity_type = request.args.get('entity_type')

    logs = db_manager.get_admin_logs(limit, entity_type)
    return jsonify(logs)


# ===== HEALTH CHECK =====

@app.route('/api/health')
def api_health():
    """API: Health check panelu administratora."""
    try:
        # Sprawdź połączenie z bazą danych
        with db_manager.get_connection() as conn:
            conn.execute("SELECT 1")

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected',
            'version': '1.0-admin-panel'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


if __name__ == '__main__':
    print("🔧 Uruchamianie Admin Panel...")
    print("🏠 Panel administratora dostępny pod adresem: http://localhost:5001")
    app.run(debug=True, host='0.0.0.0', port=5001)
