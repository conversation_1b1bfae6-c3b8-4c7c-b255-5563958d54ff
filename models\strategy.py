#!/usr/bin/env python3
"""
Strategy Model for Admin Panel
==============================

Model strategii tradingowej z pełną funkcjonalnością CRUD
i walidacją parametrów.

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
"""

import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from .database import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class Strategy:
    """
    📈 Model strategii tradingowej

    Reprezentuje strategię tradingową z wszystkimi parametrami
    konfiguracyjnymi i metodami zarządzania.
    """
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    timeframe_min: int = 60
    risk_percent: float = 2.0
    max_signals_per_day: int = 10
    signal_validity_hours: int = 48
    min_timeframe_minutes: int = 15
    default_timeframe_multiplier: int = 60
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """Walidacja po inicjalizacji."""
        self.validate()

    def validate(self) -> bool:
        """
        Waliduje parametry strategii.

        Returns:
            True jeśli walidacja przeszła pomyślnie

        Raises:
            ValueError: Jeśli parametry są nieprawidłowe
        """
        if not self.name or len(self.name.strip()) < 3:
            raise ValueError("Nazwa strategii musi mieć co najmniej 3 znaki")

        if self.timeframe_min <= 0:
            raise ValueError("Timeframe musi być większy od 0")

        if not (0.1 <= self.risk_percent <= 10.0):
            raise ValueError("Ryzyko musi być między 0.1% a 10%")

        if not (1 <= self.max_signals_per_day <= 100):
            raise ValueError("Maksymalna liczba sygnałów dziennie musi być między 1 a 100")

        if not (1 <= self.signal_validity_hours <= 168):  # max 7 dni
            raise ValueError("Ważność sygnału musi być między 1 a 168 godzin")

        if self.min_timeframe_minutes <= 0:
            raise ValueError("Minimalny timeframe musi być większy od 0")

        if self.default_timeframe_multiplier <= 0:
            raise ValueError("Mnożnik timeframe musi być większy od 0")

        return True

    def to_dict(self) -> Dict[str, Any]:
        """Konwertuje strategię do słownika."""
        data = asdict(self)
        # Konwertuj datetime do string dla JSON
        if data['created_at'] and hasattr(data['created_at'], 'isoformat'):
            data['created_at'] = data['created_at'].isoformat()
        if data['updated_at'] and hasattr(data['updated_at'], 'isoformat'):
            data['updated_at'] = data['updated_at'].isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Strategy':
        """Tworzy strategię ze słownika."""
        # Konwertuj string datetime z powrotem do datetime
        if data.get('created_at') and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('updated_at') and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])

        return cls(**data)


class StrategyManager:
    """
    🔧 Manager strategii tradingowych

    Zarządza operacjami CRUD dla strategii w bazie danych.
    """

    def __init__(self, db_manager: DatabaseManager):
        """
        Inicjalizacja StrategyManager.

        Args:
            db_manager: Instance DatabaseManager
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    def create_strategy(self, strategy: Strategy, user_ip: Optional[str] = None) -> int:
        """
        Tworzy nową strategię.

        Args:
            strategy: Obiekt strategii do utworzenia
            user_ip: IP użytkownika dla logów

        Returns:
            ID utworzonej strategii

        Raises:
            ValueError: Jeśli strategia o tej nazwie już istnieje
        """
        strategy.validate()

        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Sprawdź czy strategia o tej nazwie już istnieje
            cursor.execute("SELECT id FROM strategies WHERE name = ?", (strategy.name,))
            if cursor.fetchone():
                raise ValueError(f"Strategia o nazwie '{strategy.name}' już istnieje")

            # Wstaw nową strategię
            cursor.execute('''
                INSERT INTO strategies
                (name, description, timeframe_min, risk_percent, max_signals_per_day,
                 signal_validity_hours, min_timeframe_minutes, default_timeframe_multiplier, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                strategy.name, strategy.description, strategy.timeframe_min,
                strategy.risk_percent, strategy.max_signals_per_day,
                strategy.signal_validity_hours, strategy.min_timeframe_minutes,
                strategy.default_timeframe_multiplier, strategy.is_active
            ))

            strategy_id = cursor.lastrowid
            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'CREATE', 'strategy', strategy_id,
                new_values=strategy.to_dict(), user_ip=user_ip
            )

            self.logger.info(f"✅ Created strategy: {strategy.name} (ID: {strategy_id})")
            return strategy_id

    def get_strategy(self, strategy_id: int) -> Optional[Strategy]:
        """
        Pobiera strategię po ID.

        Args:
            strategy_id: ID strategii

        Returns:
            Obiekt Strategy lub None jeśli nie znaleziono
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM strategies WHERE id = ?", (strategy_id,))
            row = cursor.fetchone()

            if row:
                data = dict(row)
                # Konwertuj SQLite boolean (1/0) do Python boolean
                data['is_active'] = bool(data['is_active'])
                return Strategy(**data)
            return None

    def get_strategy_by_name(self, name: str) -> Optional[Strategy]:
        """
        Pobiera strategię po nazwie.

        Args:
            name: Nazwa strategii

        Returns:
            Obiekt Strategy lub None jeśli nie znaleziono
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM strategies WHERE name = ?", (name,))
            row = cursor.fetchone()

            if row:
                data = dict(row)
                # Konwertuj SQLite boolean (1/0) do Python boolean
                data['is_active'] = bool(data['is_active'])
                return Strategy(**data)
            return None

    def get_all_strategies(self, active_only: bool = False) -> List[Strategy]:
        """
        Pobiera wszystkie strategie.

        Args:
            active_only: Czy pobrać tylko aktywne strategie

        Returns:
            Lista strategii
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            query = "SELECT * FROM strategies"
            if active_only:
                query += " WHERE is_active = 1"
            query += " ORDER BY name"

            cursor.execute(query)
            strategies = []
            for row in cursor.fetchall():
                data = dict(row)
                # Konwertuj SQLite boolean (1/0) do Python boolean
                data['is_active'] = bool(data['is_active'])
                strategies.append(Strategy(**data))
            return strategies

    def update_strategy(self, strategy: Strategy, user_ip: Optional[str] = None) -> bool:
        """
        Aktualizuje strategię.

        Args:
            strategy: Obiekt strategii z zaktualizowanymi danymi
            user_ip: IP użytkownika dla logów

        Returns:
            True jeśli aktualizacja się powiodła

        Raises:
            ValueError: Jeśli strategia nie istnieje lub nazwa jest zajęta
        """
        if not strategy.id:
            raise ValueError("ID strategii jest wymagane do aktualizacji")

        strategy.validate()

        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Pobierz stare wartości dla logów
            old_strategy = self.get_strategy(strategy.id)
            if not old_strategy:
                raise ValueError(f"Strategia o ID {strategy.id} nie istnieje")

            # Sprawdź czy nazwa nie jest zajęta przez inną strategię
            cursor.execute(
                "SELECT id FROM strategies WHERE name = ? AND id != ?",
                (strategy.name, strategy.id)
            )
            if cursor.fetchone():
                raise ValueError(f"Strategia o nazwie '{strategy.name}' już istnieje")

            # Aktualizuj strategię
            cursor.execute('''
                UPDATE strategies SET
                    name = ?, description = ?, timeframe_min = ?, risk_percent = ?,
                    max_signals_per_day = ?, signal_validity_hours = ?,
                    min_timeframe_minutes = ?, default_timeframe_multiplier = ?,
                    is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                strategy.name, strategy.description, strategy.timeframe_min,
                strategy.risk_percent, strategy.max_signals_per_day,
                strategy.signal_validity_hours, strategy.min_timeframe_minutes,
                strategy.default_timeframe_multiplier, strategy.is_active,
                strategy.id
            ))

            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'UPDATE', 'strategy', strategy.id,
                old_values=old_strategy.to_dict(),
                new_values=strategy.to_dict(),
                user_ip=user_ip
            )

            self.logger.info(f"✅ Updated strategy: {strategy.name} (ID: {strategy.id})")
            return True

    def delete_strategy(self, strategy_id: int, user_ip: Optional[str] = None) -> bool:
        """
        Usuwa strategię.

        Args:
            strategy_id: ID strategii do usunięcia
            user_ip: IP użytkownika dla logów

        Returns:
            True jeśli usunięcie się powiodło

        Raises:
            ValueError: Jeśli strategia nie istnieje lub jest używana przez kanały
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Pobierz strategię dla logów
            old_strategy = self.get_strategy(strategy_id)
            if not old_strategy:
                raise ValueError(f"Strategia o ID {strategy_id} nie istnieje")

            # Sprawdź czy strategia jest używana przez kanały
            cursor.execute(
                "SELECT COUNT(*) FROM discord_channels WHERE strategy_id = ?",
                (strategy_id,)
            )
            channel_count = cursor.fetchone()[0]

            if channel_count > 0:
                raise ValueError(
                    f"Nie można usunąć strategii. Jest używana przez {channel_count} kanał(ów)"
                )

            # Usuń strategię
            cursor.execute("DELETE FROM strategies WHERE id = ?", (strategy_id,))
            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'DELETE', 'strategy', strategy_id,
                old_values=old_strategy.to_dict(),
                user_ip=user_ip
            )

            self.logger.info(f"✅ Deleted strategy: {old_strategy.name} (ID: {strategy_id})")
            return True

    def get_strategy_stats(self, strategy_id: int) -> Dict[str, Any]:
        """
        Pobiera statystyki strategii.

        Args:
            strategy_id: ID strategii

        Returns:
            Słownik ze statystykami strategii
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Pobierz liczbę kanałów używających strategii
            cursor.execute(
                "SELECT COUNT(*) FROM discord_channels WHERE strategy_id = ?",
                (strategy_id,)
            )
            channel_count = cursor.fetchone()[0]

            # Pobierz statystyki sygnałów (jeśli tabela signals ma kolumnę strategy_id)
            # Na razie zwracamy podstawowe statystyki

            return {
                'channel_count': channel_count,
                'is_in_use': channel_count > 0
            }
