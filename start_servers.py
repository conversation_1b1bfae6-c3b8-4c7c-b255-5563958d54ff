#!/usr/bin/env python3
"""
🚀 Start Servers - Discord Bybit Signal Monitor
===============================================

Uruchamia oba serwery jednocześnie:
- Trading Dashboard (port 5000)
- Admin Panel (port 5001)

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
"""

import subprocess
import sys
import time
import os
from threading import Thread

def start_dashboard():
    """Uruchamia trading dashboard."""
    print("🚀 Uruchamianie Trading Dashboard (port 5000)...")
    try:
        subprocess.run([sys.executable, "dashboard.py"], check=True)
    except KeyboardInterrupt:
        print("🛑 Trading Dashboard zatrzymany")
    except Exception as e:
        print(f"❌ Błąd Trading Dashboard: {e}")

def start_admin_panel():
    """Uruchamia panel administratora."""
    print("🔧 Uruchamianie Admin Panel (port 5001)...")
    try:
        subprocess.run([sys.executable, "admin_panel.py"], check=True)
    except KeyboardInterrupt:
        print("🛑 Admin Panel zatrzymany")
    except Exception as e:
        print(f"❌ Błąd Admin Panel: {e}")

def main():
    """Główna funkcja uruchamiająca oba serwery."""
    print("=" * 60)
    print("🚀 DISCORD BYBIT SIGNAL MONITOR - COMPLETE SYSTEM")
    print("=" * 60)
    print()
    print("📊 Trading Dashboard: http://localhost:5000")
    print("🔧 Admin Panel:       http://localhost:5001")
    print()
    print("💡 Naciśnij Ctrl+C aby zatrzymać oba serwery")
    print("=" * 60)
    print()
    
    # Sprawdź czy pliki istnieją
    if not os.path.exists("dashboard.py"):
        print("❌ Błąd: Nie znaleziono pliku dashboard.py")
        return
    
    if not os.path.exists("admin_panel.py"):
        print("❌ Błąd: Nie znaleziono pliku admin_panel.py")
        return
    
    try:
        # Uruchom oba serwery w osobnych wątkach
        dashboard_thread = Thread(target=start_dashboard, daemon=True)
        admin_thread = Thread(target=start_admin_panel, daemon=True)
        
        dashboard_thread.start()
        time.sleep(2)  # Daj czas na uruchomienie pierwszego serwera
        admin_thread.start()
        
        print("✅ Oba serwery uruchomione!")
        print()
        print("🌐 Otwórz w przeglądarce:")
        print("   • Trading Dashboard: http://localhost:5000")
        print("   • Admin Panel:       http://localhost:5001")
        print()
        
        # Czekaj na zakończenie
        try:
            dashboard_thread.join()
            admin_thread.join()
        except KeyboardInterrupt:
            print("\n🛑 Zatrzymywanie serwerów...")
            
    except KeyboardInterrupt:
        print("\n🛑 Zatrzymywanie serwerów...")
    except Exception as e:
        print(f"❌ Błąd uruchamiania: {e}")
    
    print("👋 Serwery zatrzymane. Do zobaczenia!")

if __name__ == "__main__":
    main()
