#!/usr/bin/env python3
"""
System Configuration Model for Admin Panel
==========================================

Model konfiguracji systemowej z pełną funkcjonalnością CRUD
i zarządzaniem ustawieniami aplikacji.

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
"""

import sqlite3
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from .database import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class SystemConfig:
    """
    ⚙️ Model konfiguracji systemowej
    
    Reprezentuje pojedynczy wpis konfiguracyjny systemu
    z walidacją typu i obsługą wrażliwych danych.
    """
    id: Optional[int] = None
    key: str = ""
    value: str = ""
    description: str = ""
    config_type: str = "string"  # string, integer, float, boolean
    is_sensitive: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Walidacja po inicjalizacji."""
        self.validate()
    
    def validate(self) -> bool:
        """
        Waliduje parametry konfiguracji.
        
        Returns:
            True jeśli walidacja przeszła pomyślnie
            
        Raises:
            ValueError: Jeśli parametry są nieprawidłowe
        """
        if not self.key or not self.key.strip():
            raise ValueError("Klucz konfiguracji jest wymagany")
        
        if self.config_type not in ['string', 'integer', 'float', 'boolean']:
            raise ValueError("Typ konfiguracji musi być: string, integer, float lub boolean")
        
        # Waliduj wartość według typu
        if self.value is not None and self.value != "":
            try:
                self.get_typed_value()
            except ValueError as e:
                raise ValueError(f"Nieprawidłowa wartość dla typu {self.config_type}: {e}")
        
        return True
    
    def get_typed_value(self) -> Union[str, int, float, bool]:
        """
        Zwraca wartość skonwertowaną do odpowiedniego typu.
        
        Returns:
            Wartość w odpowiednim typie
            
        Raises:
            ValueError: Jeśli konwersja się nie powiedzie
        """
        if self.value is None or self.value == "":
            return None
        
        if self.config_type == "string":
            return str(self.value)
        elif self.config_type == "integer":
            return int(self.value)
        elif self.config_type == "float":
            return float(self.value)
        elif self.config_type == "boolean":
            if isinstance(self.value, bool):
                return self.value
            return str(self.value).lower() in ['true', '1', 'yes', 'on']
        else:
            return str(self.value)
    
    def set_typed_value(self, value: Union[str, int, float, bool]):
        """
        Ustawia wartość z automatyczną konwersją do string.
        
        Args:
            value: Wartość do ustawienia
        """
        if value is None:
            self.value = ""
        elif isinstance(value, bool):
            self.value = "true" if value else "false"
        else:
            self.value = str(value)
    
    def get_display_value(self) -> str:
        """
        Zwraca wartość do wyświetlenia (maskuje wrażliwe dane).
        
        Returns:
            Wartość do wyświetlenia
        """
        if self.is_sensitive and self.value:
            return "*" * min(len(self.value), 8)
        return self.value
    
    def to_dict(self) -> Dict[str, Any]:
        """Konwertuje konfigurację do słownika."""
        data = asdict(self)
        # Konwertuj datetime do string dla JSON
        if data['created_at']:
            data['created_at'] = data['created_at'].isoformat()
        if data['updated_at']:
            data['updated_at'] = data['updated_at'].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SystemConfig':
        """Tworzy konfigurację ze słownika."""
        # Konwertuj string datetime z powrotem do datetime
        if data.get('created_at') and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('updated_at') and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)


class ConfigManager:
    """
    🔧 Manager konfiguracji systemowej
    
    Zarządza operacjami CRUD dla konfiguracji systemowej
    z obsługą różnych typów danych i wrażliwych informacji.
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Inicjalizacja ConfigManager.
        
        Args:
            db_manager: Instance DatabaseManager
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self._config_cache = {}
        self._cache_timestamp = None
    
    def create_config(self, config: SystemConfig, user_ip: Optional[str] = None) -> int:
        """
        Tworzy nową konfigurację.
        
        Args:
            config: Obiekt konfiguracji do utworzenia
            user_ip: IP użytkownika dla logów
            
        Returns:
            ID utworzonej konfiguracji
            
        Raises:
            ValueError: Jeśli konfiguracja o tym kluczu już istnieje
        """
        config.validate()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Sprawdź czy konfiguracja o tym kluczu już istnieje
            cursor.execute("SELECT id FROM system_config WHERE key = ?", (config.key,))
            if cursor.fetchone():
                raise ValueError(f"Konfiguracja o kluczu '{config.key}' już istnieje")
            
            # Wstaw nową konfigurację
            cursor.execute('''
                INSERT INTO system_config 
                (key, value, description, config_type, is_sensitive)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                config.key, config.value, config.description,
                config.config_type, config.is_sensitive
            ))
            
            config_id = cursor.lastrowid
            conn.commit()
            
            # Wyczyść cache
            self._clear_cache()
            
            # Loguj akcję (bez wrażliwych danych)
            log_data = config.to_dict()
            if config.is_sensitive:
                log_data['value'] = '***SENSITIVE***'
            
            self.db_manager.log_admin_action(
                'CREATE', 'config', config_id, 
                new_values=log_data, user_ip=user_ip
            )
            
            self.logger.info(f"✅ Created config: {config.key} (ID: {config_id})")
            return config_id
    
    def get_config(self, config_id: int) -> Optional[SystemConfig]:
        """
        Pobiera konfigurację po ID.
        
        Args:
            config_id: ID konfiguracji
            
        Returns:
            Obiekt SystemConfig lub None jeśli nie znaleziono
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM system_config WHERE id = ?", (config_id,))
            row = cursor.fetchone()
            
            if row:
                return SystemConfig(**dict(row))
            return None
    
    def get_config_by_key(self, key: str) -> Optional[SystemConfig]:
        """
        Pobiera konfigurację po kluczu.
        
        Args:
            key: Klucz konfiguracji
            
        Returns:
            Obiekt SystemConfig lub None jeśli nie znaleziono
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM system_config WHERE key = ?", (key,))
            row = cursor.fetchone()
            
            if row:
                return SystemConfig(**dict(row))
            return None
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Pobiera wartość konfiguracji w odpowiednim typie.
        
        Args:
            key: Klucz konfiguracji
            default: Wartość domyślna jeśli nie znaleziono
            
        Returns:
            Wartość konfiguracji w odpowiednim typie
        """
        config = self.get_config_by_key(key)
        if config:
            try:
                return config.get_typed_value()
            except ValueError:
                self.logger.warning(f"Invalid config value for key '{key}', returning default")
                return default
        return default
    
    def get_all_configs(self, include_sensitive: bool = False) -> List[SystemConfig]:
        """
        Pobiera wszystkie konfiguracje.
        
        Args:
            include_sensitive: Czy uwzględnić wrażliwe konfiguracje
            
        Returns:
            Lista konfiguracji
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM system_config"
            if not include_sensitive:
                query += " WHERE is_sensitive = 0"
            query += " ORDER BY key"
            
            cursor.execute(query)
            return [SystemConfig(**dict(row)) for row in cursor.fetchall()]
    
    def update_config(self, config: SystemConfig, user_ip: Optional[str] = None) -> bool:
        """
        Aktualizuje konfigurację.
        
        Args:
            config: Obiekt konfiguracji z zaktualizowanymi danymi
            user_ip: IP użytkownika dla logów
            
        Returns:
            True jeśli aktualizacja się powiodła
            
        Raises:
            ValueError: Jeśli konfiguracja nie istnieje lub klucz jest zajęty
        """
        if not config.id:
            raise ValueError("ID konfiguracji jest wymagane do aktualizacji")
        
        config.validate()
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Pobierz stare wartości dla logów
            old_config = self.get_config(config.id)
            if not old_config:
                raise ValueError(f"Konfiguracja o ID {config.id} nie istnieje")
            
            # Sprawdź czy klucz nie jest zajęty przez inną konfigurację
            cursor.execute(
                "SELECT id FROM system_config WHERE key = ? AND id != ?", 
                (config.key, config.id)
            )
            if cursor.fetchone():
                raise ValueError(f"Konfiguracja o kluczu '{config.key}' już istnieje")
            
            # Aktualizuj konfigurację
            cursor.execute('''
                UPDATE system_config SET
                    key = ?, value = ?, description = ?, config_type = ?,
                    is_sensitive = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                config.key, config.value, config.description,
                config.config_type, config.is_sensitive, config.id
            ))
            
            conn.commit()
            
            # Wyczyść cache
            self._clear_cache()
            
            # Loguj akcję (bez wrażliwych danych)
            old_log_data = old_config.to_dict()
            new_log_data = config.to_dict()
            
            if old_config.is_sensitive:
                old_log_data['value'] = '***SENSITIVE***'
            if config.is_sensitive:
                new_log_data['value'] = '***SENSITIVE***'
            
            self.db_manager.log_admin_action(
                'UPDATE', 'config', config.id,
                old_values=old_log_data,
                new_values=new_log_data,
                user_ip=user_ip
            )
            
            self.logger.info(f"✅ Updated config: {config.key} (ID: {config.id})")
            return True
    
    def delete_config(self, config_id: int, user_ip: Optional[str] = None) -> bool:
        """
        Usuwa konfigurację.
        
        Args:
            config_id: ID konfiguracji do usunięcia
            user_ip: IP użytkownika dla logów
            
        Returns:
            True jeśli usunięcie się powiodło
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Pobierz konfigurację dla logów
            old_config = self.get_config(config_id)
            if not old_config:
                raise ValueError(f"Konfiguracja o ID {config_id} nie istnieje")
            
            # Usuń konfigurację
            cursor.execute("DELETE FROM system_config WHERE id = ?", (config_id,))
            conn.commit()
            
            # Wyczyść cache
            self._clear_cache()
            
            # Loguj akcję (bez wrażliwych danych)
            log_data = old_config.to_dict()
            if old_config.is_sensitive:
                log_data['value'] = '***SENSITIVE***'
            
            self.db_manager.log_admin_action(
                'DELETE', 'config', config_id,
                old_values=log_data,
                user_ip=user_ip
            )
            
            self.logger.info(f"✅ Deleted config: {old_config.key} (ID: {config_id})")
            return True
    
    def set_config_value(self, key: str, value: Any, user_ip: Optional[str] = None) -> bool:
        """
        Ustawia wartość konfiguracji (tworzy jeśli nie istnieje).
        
        Args:
            key: Klucz konfiguracji
            value: Wartość do ustawienia
            user_ip: IP użytkownika dla logów
            
        Returns:
            True jeśli operacja się powiodła
        """
        config = self.get_config_by_key(key)
        
        if config:
            # Aktualizuj istniejącą konfigurację
            config.set_typed_value(value)
            return self.update_config(config, user_ip)
        else:
            # Utwórz nową konfigurację
            new_config = SystemConfig(
                key=key,
                value=str(value),
                description=f"Auto-created config for {key}",
                config_type="string"
            )
            self.create_config(new_config, user_ip)
            return True
    
    def get_env_configs(self) -> Dict[str, str]:
        """
        Pobiera konfiguracje jako słownik dla zmiennych środowiskowych.
        
        Returns:
            Słownik konfiguracji w formacie klucz: wartość
        """
        configs = self.get_all_configs(include_sensitive=True)
        return {config.key.upper(): config.value for config in configs if config.value}
    
    def _clear_cache(self):
        """Czyści cache konfiguracji."""
        self._config_cache.clear()
        self._cache_timestamp = None
