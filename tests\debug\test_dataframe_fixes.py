#!/usr/bin/env python3
"""
Test naprawy DataFrame z NULL wartościami i problemu z to_dict().
Debug tests for DataFrame handling and conversion issues.
"""

import pytest
import sqlite3
import pandas as pd
import numpy as np
import tempfile
import os


class TestDataFrameFixesDebug:
    """Debug tests for DataFrame handling and conversion issues."""
    
    def test_dataframe_null_handling(self, test_db):
        """Test różnych sposobów obsługi NULL w DataFrame."""
        conn = sqlite3.connect(test_db)
        
        print(f"\n🔍 TEST NAPRAWY DATAFRAME Z NULL")
        print("=" * 40)
        
        # Test 1: Standardowy pandas
        print("\n1️⃣ Standardowy pandas:")
        df1 = pd.read_sql_query("SELECT id, pair, status, pnl FROM signals ORDER BY id DESC LIMIT 5", conn)
        print(df1[['id', 'pair', 'status', 'pnl']])
        print(f"Typy: {df1.dtypes}")
        
        # Test 2: Raw SQL z manual conversion
        print("\n2️⃣ Raw SQL z manual conversion:")
        cursor = conn.cursor()
        cursor.execute("SELECT id, pair, status, pnl FROM signals ORDER BY id DESC LIMIT 5")
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        data = []
        for row in rows:
            row_dict = {}
            for i, col in enumerate(columns):
                row_dict[col] = row[i]
            data.append(row_dict)
        
        df2 = pd.DataFrame(data)
        print(df2[['id', 'pair', 'status', 'pnl']])
        print(f"Typy: {df2.dtypes}")
        
        # Test 3: Pandas z dtype=object dla pnl
        print("\n3️⃣ Pandas z dtype=object:")
        df3 = pd.read_sql_query("SELECT id, pair, status, pnl FROM signals ORDER BY id DESC LIMIT 5", conn, dtype={'pnl': 'object'})
        print(df3[['id', 'pair', 'status', 'pnl']])
        print(f"Typy: {df3.dtypes}")
        
        # Test 4: Sprawdź rzeczywiste wartości
        print("\n4️⃣ Rzeczywiste wartości z bazy:")
        cursor.execute("SELECT id, pair, status, pnl FROM signals ORDER BY id DESC LIMIT 5")
        for row in cursor.fetchall():
            print(f"ID {row[0]}: {row[1]} - {row[2]} - PnL: {repr(row[3])}")
        
        # Test 5: Konwersja do JSON
        print("\n5️⃣ Test konwersji do JSON:")
        signals = df3.to_dict('records')
        for signal in signals[:3]:
            print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
        
        conn.close()
        
        # Assertions for automated testing
        assert not df1.empty, "DataFrame should not be empty"
        assert not df2.empty, "Manual DataFrame should not be empty"
        assert not df3.empty, "Object dtype DataFrame should not be empty"
        assert len(signals) > 0, "Converted signals should not be empty"
    
    def test_to_dict_conversion_issue(self, test_db):
        """Test problemu z konwersją DataFrame do dict."""
        conn = sqlite3.connect(test_db)
        
        print(f"\n🔍 TEST PROBLEMU Z to_dict()")
        print("=" * 40)
        
        # Test 1: Raw SQL
        cursor = conn.cursor()
        cursor.execute("SELECT id, pair, status, pnl FROM signals ORDER BY id DESC LIMIT 3")
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        data = []
        for row in rows:
            row_dict = {}
            for i, col in enumerate(columns):
                row_dict[col] = row[i]
            data.append(row_dict)
        
        df = pd.DataFrame(data)
        
        print("DataFrame:")
        print(df[['id', 'pair', 'status', 'pnl']])
        print(f"Typy: {df.dtypes}")
        print()
        
        # Test 2: to_dict('records')
        print("to_dict('records'):")
        signals = df.to_dict('records')
        for signal in signals:
            print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
        print()
        
        # Test 3: Sprawdź czy są NaN
        print("Sprawdzenie NaN:")
        for signal in signals:
            pnl = signal['pnl']
            if pnl is None:
                print(f"ID {signal['id']}: PnL is None")
            elif pd.isna(pnl):
                print(f"ID {signal['id']}: PnL is NaN")
            elif isinstance(pnl, float) and pnl == 0.0:
                print(f"ID {signal['id']}: PnL is 0.0")
            else:
                print(f"ID {signal['id']}: PnL = {pnl}")
        print()
        
        # Test 4: Sprawdź czy problem jest w fillna
        print("Test fillna:")
        df_filled = df.fillna(0.0)
        signals_filled = df_filled.to_dict('records')
        for signal in signals_filled[:3]:
            print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
        print()
        
        # Test 5: Sprawdź czy problem jest w pandas dtype
        print("Test z dtype=object:")
        df_obj = pd.DataFrame(data, dtype=object)
        signals_obj = df_obj.to_dict('records')
        for signal in signals_obj[:3]:
            print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
        
        conn.close()
        
        # Assertions for automated testing
        assert len(signals) > 0, "Signals should not be empty"
        assert len(signals_filled) > 0, "Filled signals should not be empty"
        assert len(signals_obj) > 0, "Object signals should not be empty"
    
    def test_nan_vs_none_handling(self, test_db):
        """Test różnic między NaN a None w pandas."""
        conn = sqlite3.connect(test_db)
        
        print(f"\n🔍 TEST NaN vs None")
        print("=" * 40)
        
        # Utwórz test DataFrame z różnymi typami NULL
        test_data = [
            {'id': 1, 'pnl': None},
            {'id': 2, 'pnl': np.nan},
            {'id': 3, 'pnl': 100.0},
            {'id': 4, 'pnl': 0.0},
        ]
        
        df = pd.DataFrame(test_data)
        
        print("Test DataFrame:")
        print(df)
        print(f"Typy: {df.dtypes}")
        print()
        
        # Test konwersji
        signals = df.to_dict('records')
        
        print("Po konwersji to_dict:")
        for signal in signals:
            pnl = signal['pnl']
            print(f"ID {signal['id']}: PnL = {repr(pnl)} (type: {type(pnl)}, isna: {pd.isna(pnl)})")
        
        conn.close()
    
    @pytest.mark.debug
    def test_production_dataframe_handling(self, test_db):
        """Test obsługi DataFrame w środowisku produkcyjnym."""
        conn = sqlite3.connect(test_db)
        
        print(f"\n🔍 TEST PRODUKCYJNEJ OBSŁUGI DATAFRAME")
        print("=" * 40)
        
        # Symuluj rzeczywiste zapytanie z dashboard
        query = """
        SELECT id, pair, direction, entry_price, tp_price, sl_price, 
               status, timestamp, pnl, pnl_percent, timeframe, message_content
        FROM signals 
        ORDER BY timestamp DESC 
        LIMIT 10
        """
        
        # Test różnych metod ładowania
        methods = [
            ("Standard pandas", lambda: pd.read_sql_query(query, conn)),
            ("Object dtype", lambda: pd.read_sql_query(query, conn, dtype={'pnl': 'object', 'pnl_percent': 'object'})),
            ("Manual conversion", lambda: self._manual_sql_to_dataframe(conn, query))
        ]
        
        for method_name, method_func in methods:
            print(f"\n{method_name}:")
            try:
                df = method_func()
                signals = df.to_dict('records')
                
                print(f"  Loaded {len(signals)} signals")
                
                # Sprawdź typy PnL
                pnl_types = set()
                for signal in signals:
                    pnl = signal.get('pnl')
                    if pnl is not None:
                        pnl_types.add(type(pnl).__name__)
                
                print(f"  PnL types found: {pnl_types}")
                
                # Sprawdź czy są problemy z JSON serialization
                import json
                try:
                    json.dumps(signals[:1], default=str)
                    print(f"  ✅ JSON serializable")
                except Exception as e:
                    print(f"  ❌ JSON error: {e}")
                    
            except Exception as e:
                print(f"  ❌ Method failed: {e}")
        
        conn.close()
    
    def _manual_sql_to_dataframe(self, conn, query):
        """Manual SQL to DataFrame conversion."""
        cursor = conn.cursor()
        cursor.execute(query)
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        data = []
        for row in rows:
            row_dict = {}
            for i, col in enumerate(columns):
                row_dict[col] = row[i]
            data.append(row_dict)
        
        return pd.DataFrame(data)


def test_dataframe_fix_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    conn = sqlite3.connect('signals.db')
    
    print("🔍 TEST NAPRAWY DATAFRAME Z NULL")
    print("=" * 40)
    
    # Test 1: Standardowy pandas
    print("\n1️⃣ Standardowy pandas:")
    df1 = pd.read_sql_query("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5", conn)
    print(df1[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df1.dtypes}")
    
    # Test 2: Raw SQL z manual conversion
    print("\n2️⃣ Raw SQL z manual conversion:")
    cursor = conn.cursor()
    cursor.execute("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5")
    columns = [description[0] for description in cursor.description]
    rows = cursor.fetchall()
    
    data = []
    for row in rows:
        row_dict = {}
        for i, col in enumerate(columns):
            row_dict[col] = row[i]
        data.append(row_dict)
    
    df2 = pd.DataFrame(data)
    print(df2[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df2.dtypes}")
    
    # Test 3: Pandas z dtype=object dla pnl
    print("\n3️⃣ Pandas z dtype=object:")
    df3 = pd.read_sql_query("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5", conn, dtype={'pnl': 'object'})
    print(df3[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df3.dtypes}")
    
    # Test 4: Sprawdź rzeczywiste wartości
    print("\n4️⃣ Rzeczywiste wartości z bazy:")
    cursor.execute("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 5")
    for row in cursor.fetchall():
        print(f"ID {row[0]}: {row[1]} - {row[2]} - PnL: {repr(row[3])}")
    
    # Test 5: Konwersja do JSON
    print("\n5️⃣ Test konwersji do JSON:")
    signals = df3.to_dict('records')
    for signal in signals[:3]:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    
    conn.close()


def test_to_dict_issue_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    conn = sqlite3.connect('signals.db')
    
    print("🔍 TEST PROBLEMU Z to_dict()")
    print("=" * 40)
    
    # Test 1: Raw SQL
    cursor = conn.cursor()
    cursor.execute("SELECT id, pair, status, pnl FROM signals WHERE id >= 25 ORDER BY id DESC LIMIT 3")
    columns = [description[0] for description in cursor.description]
    rows = cursor.fetchall()
    
    data = []
    for row in rows:
        row_dict = {}
        for i, col in enumerate(columns):
            row_dict[col] = row[i]
        data.append(row_dict)
    
    df = pd.DataFrame(data)
    
    print("DataFrame:")
    print(df[['id', 'pair', 'status', 'pnl']])
    print(f"Typy: {df.dtypes}")
    print()
    
    # Test 2: to_dict('records')
    print("to_dict('records'):")
    signals = df.to_dict('records')
    for signal in signals:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    print()
    
    # Test 3: Sprawdź czy są NaN
    print("Sprawdzenie NaN:")
    for signal in signals:
        pnl = signal['pnl']
        if pnl is None:
            print(f"ID {signal['id']}: PnL is None")
        elif pd.isna(pnl):
            print(f"ID {signal['id']}: PnL is NaN")
        elif isinstance(pnl, float) and pnl == 0.0:
            print(f"ID {signal['id']}: PnL is 0.0")
        else:
            print(f"ID {signal['id']}: PnL = {pnl}")
    print()
    
    # Test 4: Sprawdź czy problem jest w fillna
    print("Test fillna:")
    df_filled = df.fillna(0.0)
    signals_filled = df_filled.to_dict('records')
    for signal in signals_filled[:3]:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    print()
    
    # Test 5: Sprawdź czy problem jest w pandas dtype
    print("Test z dtype=object:")
    df_obj = pd.DataFrame(data, dtype=object)
    signals_obj = df_obj.to_dict('records')
    for signal in signals_obj[:3]:
        print(f"ID {signal['id']}: PnL = {repr(signal['pnl'])} (type: {type(signal['pnl'])})")
    
    conn.close()


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "dict":
        test_to_dict_issue_manual()
    else:
        test_dataframe_fix_manual()
