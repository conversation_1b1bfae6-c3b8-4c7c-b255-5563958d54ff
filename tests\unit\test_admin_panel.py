#!/usr/bin/env python3
"""
Unit Tests for Admin Panel
==========================

Testy jednostkowe dla panelu administratora:
- <PERSON><PERSON> danych (Strategy, Channel, Config)
- Managery (StrategyManager, ChannelManager, ConfigManager)
- API endpoints
- Walida<PERSON>ja danych

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
"""

import pytest
import tempfile
import os
import sqlite3
from datetime import datetime
from unittest.mock import Mock, patch

# Import modeli i managerów
from models.database import DatabaseManager
from models.strategy import Strategy, StrategyManager
from models.channel import DiscordChannel, ChannelManager, BotWhitelist
from models.config import SystemConfig, ConfigManager


class TestDatabaseManager:
    """Testy dla DatabaseManager."""
    
    def test_database_initialization(self):
        """Test inicjalizacji bazy danych."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        try:
            db_manager = DatabaseManager(db_path)
            
            # Sprawdź czy tabele zostały utworzone
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Sprawdź tabele
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                expected_tables = [
                    'strategies', 'discord_channels', 'bot_whitelist', 
                    'system_config', 'admin_logs'
                ]
                
                for table in expected_tables:
                    assert table in tables, f"Tabela {table} nie została utworzona"
        
        finally:
            os.unlink(db_path)
    
    def test_admin_logging(self):
        """Test logowania akcji administratora."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        try:
            db_manager = DatabaseManager(db_path)
            
            # Zaloguj akcję
            db_manager.log_admin_action(
                'CREATE', 'strategy', 1,
                new_values={'name': 'Test Strategy'},
                user_ip='127.0.0.1'
            )
            
            # Sprawdź czy log został zapisany
            logs = db_manager.get_admin_logs(limit=1)
            assert len(logs) == 1
            assert logs[0]['action'] == 'CREATE'
            assert logs[0]['entity_type'] == 'strategy'
            assert logs[0]['entity_id'] == 1
            assert logs[0]['user_ip'] == '127.0.0.1'
        
        finally:
            os.unlink(db_path)


class TestStrategy:
    """Testy dla modelu Strategy."""
    
    def test_strategy_creation(self):
        """Test tworzenia strategii."""
        strategy = Strategy(
            name="Test Strategy",
            description="Test description",
            timeframe_min=60,
            risk_percent=2.0
        )
        
        assert strategy.name == "Test Strategy"
        assert strategy.description == "Test description"
        assert strategy.timeframe_min == 60
        assert strategy.risk_percent == 2.0
        assert strategy.is_active is True
    
    def test_strategy_validation(self):
        """Test walidacji strategii."""
        # Test prawidłowej strategii
        strategy = Strategy(name="Valid Strategy", timeframe_min=60, risk_percent=2.0)
        assert strategy.validate() is True
        
        # Test nieprawidłowej nazwy
        with pytest.raises(ValueError, match="Nazwa strategii musi mieć co najmniej 3 znaki"):
            Strategy(name="AB", timeframe_min=60, risk_percent=2.0)
        
        # Test nieprawidłowego timeframe
        with pytest.raises(ValueError, match="Timeframe musi być większy od 0"):
            Strategy(name="Test Strategy", timeframe_min=0, risk_percent=2.0)
        
        # Test nieprawidłowego ryzyka
        with pytest.raises(ValueError, match="Ryzyko musi być między 0.1% a 10%"):
            Strategy(name="Test Strategy", timeframe_min=60, risk_percent=15.0)
    
    def test_strategy_to_dict(self):
        """Test konwersji strategii do słownika."""
        strategy = Strategy(
            id=1,
            name="Test Strategy",
            timeframe_min=60,
            risk_percent=2.0,
            created_at=datetime(2025, 1, 1, 12, 0, 0)
        )
        
        data = strategy.to_dict()
        assert data['id'] == 1
        assert data['name'] == "Test Strategy"
        assert data['timeframe_min'] == 60
        assert data['risk_percent'] == 2.0
        assert data['created_at'] == "2025-01-01T12:00:00"
    
    def test_strategy_from_dict(self):
        """Test tworzenia strategii ze słownika."""
        data = {
            'id': 1,
            'name': "Test Strategy",
            'timeframe_min': 60,
            'risk_percent': 2.0,
            'created_at': "2025-01-01T12:00:00"
        }
        
        strategy = Strategy.from_dict(data)
        assert strategy.id == 1
        assert strategy.name == "Test Strategy"
        assert strategy.timeframe_min == 60
        assert strategy.risk_percent == 2.0
        assert strategy.created_at == datetime(2025, 1, 1, 12, 0, 0)


class TestStrategyManager:
    """Testy dla StrategyManager."""
    
    @pytest.fixture
    def strategy_manager(self):
        """Fixture dla StrategyManager z tymczasową bazą danych."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        db_manager = DatabaseManager(db_path)
        manager = StrategyManager(db_manager)
        
        yield manager
        
        os.unlink(db_path)
    
    def test_create_strategy(self, strategy_manager):
        """Test tworzenia strategii."""
        strategy = Strategy(
            name="Test Strategy",
            description="Test description",
            timeframe_min=60,
            risk_percent=2.0
        )
        
        strategy_id = strategy_manager.create_strategy(strategy)
        assert strategy_id is not None
        assert strategy_id > 0
        
        # Sprawdź czy strategia została zapisana
        saved_strategy = strategy_manager.get_strategy(strategy_id)
        assert saved_strategy is not None
        assert saved_strategy.name == "Test Strategy"
        assert saved_strategy.description == "Test description"
    
    def test_create_duplicate_strategy(self, strategy_manager):
        """Test tworzenia strategii z duplikowaną nazwą."""
        strategy1 = Strategy(name="Duplicate Strategy", timeframe_min=60, risk_percent=2.0)
        strategy2 = Strategy(name="Duplicate Strategy", timeframe_min=30, risk_percent=1.5)
        
        strategy_manager.create_strategy(strategy1)
        
        with pytest.raises(ValueError, match="Strategia o nazwie 'Duplicate Strategy' już istnieje"):
            strategy_manager.create_strategy(strategy2)
    
    def test_get_all_strategies(self, strategy_manager):
        """Test pobierania wszystkich strategii."""
        # Utwórz kilka strategii
        strategies_data = [
            Strategy(name="Strategy 1", timeframe_min=60, risk_percent=2.0, is_active=True),
            Strategy(name="Strategy 2", timeframe_min=30, risk_percent=1.5, is_active=False),
            Strategy(name="Strategy 3", timeframe_min=240, risk_percent=3.0, is_active=True)
        ]
        
        for strategy in strategies_data:
            strategy_manager.create_strategy(strategy)
        
        # Pobierz wszystkie strategie
        all_strategies = strategy_manager.get_all_strategies()
        assert len(all_strategies) == 3
        
        # Pobierz tylko aktywne strategie
        active_strategies = strategy_manager.get_all_strategies(active_only=True)
        assert len(active_strategies) == 2
        
        for strategy in active_strategies:
            assert strategy.is_active is True
    
    def test_update_strategy(self, strategy_manager):
        """Test aktualizacji strategii."""
        # Utwórz strategię
        strategy = Strategy(name="Original Strategy", timeframe_min=60, risk_percent=2.0)
        strategy_id = strategy_manager.create_strategy(strategy)
        
        # Pobierz i zaktualizuj strategię
        saved_strategy = strategy_manager.get_strategy(strategy_id)
        saved_strategy.name = "Updated Strategy"
        saved_strategy.risk_percent = 3.0
        
        success = strategy_manager.update_strategy(saved_strategy)
        assert success is True
        
        # Sprawdź czy zmiany zostały zapisane
        updated_strategy = strategy_manager.get_strategy(strategy_id)
        assert updated_strategy.name == "Updated Strategy"
        assert updated_strategy.risk_percent == 3.0
    
    def test_delete_strategy(self, strategy_manager):
        """Test usuwania strategii."""
        # Utwórz strategię
        strategy = Strategy(name="To Delete Strategy", timeframe_min=60, risk_percent=2.0)
        strategy_id = strategy_manager.create_strategy(strategy)
        
        # Usuń strategię
        success = strategy_manager.delete_strategy(strategy_id)
        assert success is True
        
        # Sprawdź czy strategia została usunięta
        deleted_strategy = strategy_manager.get_strategy(strategy_id)
        assert deleted_strategy is None


class TestDiscordChannel:
    """Testy dla modelu DiscordChannel."""
    
    def test_channel_creation(self):
        """Test tworzenia kanału Discord."""
        channel = DiscordChannel(
            channel_id="123456789012345678",
            channel_name="test-channel",
            guild_id="987654321098765432",
            strategy_id=1
        )
        
        assert channel.channel_id == "123456789012345678"
        assert channel.channel_name == "test-channel"
        assert channel.guild_id == "987654321098765432"
        assert channel.strategy_id == 1
        assert channel.is_active is True
        assert channel.allow_bot_messages is False
    
    def test_channel_validation(self):
        """Test walidacji kanału Discord."""
        # Test prawidłowego kanału
        channel = DiscordChannel(
            channel_id="123456789012345678",
            channel_name="test-channel"
        )
        assert channel.validate() is True
        
        # Test pustego channel_id
        with pytest.raises(ValueError, match="ID kanału Discord jest wymagane"):
            DiscordChannel(channel_id="", channel_name="test-channel")
        
        # Test nieprawidłowego channel_id (nie liczba)
        with pytest.raises(ValueError, match="ID kanału Discord musi być liczbą"):
            DiscordChannel(channel_id="invalid-id", channel_name="test-channel")
        
        # Test krótkiej nazwy kanału
        with pytest.raises(ValueError, match="Nazwa kanału musi mieć co najmniej 2 znaki"):
            DiscordChannel(channel_id="123456789012345678", channel_name="a")


class TestBotWhitelist:
    """Testy dla modelu BotWhitelist."""
    
    def test_bot_validation(self):
        """Test walidacji bota."""
        # Test prawidłowego bota
        bot = BotWhitelist(
            channel_id=1,
            bot_id="123456789012345678",
            bot_name="TestBot"
        )
        assert bot.validate() is True
        
        # Test pustego bot_id
        with pytest.raises(ValueError, match="ID bota Discord jest wymagane"):
            BotWhitelist(channel_id=1, bot_id="")
        
        # Test nieprawidłowego bot_id (nie liczba)
        with pytest.raises(ValueError, match="ID bota Discord musi być liczbą"):
            BotWhitelist(channel_id=1, bot_id="invalid-bot-id")


class TestSystemConfig:
    """Testy dla modelu SystemConfig."""
    
    def test_config_creation(self):
        """Test tworzenia konfiguracji."""
        config = SystemConfig(
            key="test_key",
            value="test_value",
            description="Test configuration",
            config_type="string"
        )
        
        assert config.key == "test_key"
        assert config.value == "test_value"
        assert config.description == "Test configuration"
        assert config.config_type == "string"
        assert config.is_sensitive is False
    
    def test_config_validation(self):
        """Test walidacji konfiguracji."""
        # Test prawidłowej konfiguracji
        config = SystemConfig(key="valid_key", value="valid_value")
        assert config.validate() is True
        
        # Test pustego klucza
        with pytest.raises(ValueError, match="Klucz konfiguracji jest wymagany"):
            SystemConfig(key="", value="test_value")
        
        # Test nieprawidłowego typu
        with pytest.raises(ValueError, match="Typ konfiguracji musi być"):
            SystemConfig(key="test_key", value="test_value", config_type="invalid_type")
    
    def test_typed_values(self):
        """Test konwersji typów wartości."""
        # Test string
        config_str = SystemConfig(key="str_key", value="test", config_type="string")
        assert config_str.get_typed_value() == "test"
        
        # Test integer
        config_int = SystemConfig(key="int_key", value="42", config_type="integer")
        assert config_int.get_typed_value() == 42
        
        # Test float
        config_float = SystemConfig(key="float_key", value="3.14", config_type="float")
        assert config_float.get_typed_value() == 3.14
        
        # Test boolean
        config_bool = SystemConfig(key="bool_key", value="true", config_type="boolean")
        assert config_bool.get_typed_value() is True
        
        config_bool_false = SystemConfig(key="bool_key", value="false", config_type="boolean")
        assert config_bool_false.get_typed_value() is False
    
    def test_set_typed_value(self):
        """Test ustawiania wartości z automatyczną konwersją."""
        config = SystemConfig(key="test_key", value="", config_type="boolean")
        
        config.set_typed_value(True)
        assert config.value == "true"
        
        config.set_typed_value(False)
        assert config.value == "false"
        
        config.set_typed_value(42)
        assert config.value == "42"
    
    def test_display_value(self):
        """Test maskowania wrażliwych wartości."""
        # Test normalnej wartości
        config_normal = SystemConfig(
            key="normal_key", 
            value="normal_value", 
            is_sensitive=False
        )
        assert config_normal.get_display_value() == "normal_value"
        
        # Test wrażliwej wartości
        config_sensitive = SystemConfig(
            key="sensitive_key", 
            value="secret_password", 
            is_sensitive=True
        )
        assert config_sensitive.get_display_value() == "********"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
