"""
Discord Bybit Signal Monitor - Test Suite
==========================================

Comprehensive test suite for the Discord Bybit Signal Monitor application.

Test Structure:
- unit/: Unit tests for individual components
- integration/: Integration tests for system components
- api/: API endpoint tests
- debug/: Debug and troubleshooting tests

Usage:
    pytest tests/                    # Run all tests
    pytest tests/unit/              # Run unit tests only
    pytest tests/api/               # Run API tests only
    pytest tests/debug/             # Run debug tests only
    pytest -v tests/                # Verbose output
    pytest --tb=short tests/        # Short traceback format

Requirements:
    - pytest
    - requests
    - pandas
    - sqlite3
"""

__version__ = "2.0.0"
__author__ = "Discord Bybit Signal Monitor Team"
