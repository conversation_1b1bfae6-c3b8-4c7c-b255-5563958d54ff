#!/usr/bin/env python3
"""
Test API sygnałów - sprawdź jak zwracane są dane PnL.
Zorganizowany test z pytest framework.
"""

import pytest
import requests
import json
from typing import List, Dict, Any


class TestSignalsAPI:
    """Test suite for Signals API endpoints."""
    
    def test_signals_api_basic(self, base_url):
        """Test podstawowej funkcjonalności API sygnałów."""
        response = requests.get(f'{base_url}/api/signals?limit=10')
        
        assert response.status_code == 200, f"API returned {response.status_code}"
        
        signals = response.json()
        assert isinstance(signals, list), "Response should be a list"
        assert len(signals) <= 10, "Should respect limit parameter"
        
        if signals:
            signal = signals[0]
            required_fields = ['id', 'pair', 'status']
            for field in required_fields:
                assert field in signal, f"Signal missing required field: {field}"
    
    def test_signals_pnl_format(self, base_url):
        """Test formatu danych PnL w API sygnałów."""
        response = requests.get(f'{base_url}/api/signals?limit=10')
        assert response.status_code == 200
        
        signals = response.json()
        
        for signal in signals[:5]:
            # Sprawdź podstawowe pola
            assert 'id' in signal
            assert 'pair' in signal
            assert 'status' in signal
            
            # Sprawdź format PnL
            pnl = signal.get('pnl')
            if pnl is not None:
                assert isinstance(pnl, (int, float)), f"PnL should be numeric, got {type(pnl)}"
            
            # Sprawdź PnL percent
            pnl_percent = signal.get('pnl_percent')
            if pnl_percent is not None:
                assert isinstance(pnl_percent, (int, float)), f"PnL percent should be numeric, got {type(pnl_percent)}"
    
    def test_signals_with_pnl(self, base_url):
        """Test sygnałów z rzeczywistym PnL."""
        response = requests.get(f'{base_url}/api/signals?limit=30')
        assert response.status_code == 200
        
        signals = response.json()
        signals_with_pnl = [s for s in signals if s.get('pnl') is not None]
        
        # Sprawdź czy sygnały z PnL mają odpowiedni status
        for signal in signals_with_pnl:
            status = signal['status']
            assert status in ['TP_HIT', 'SL_HIT', 'EXPIRED'], \
                f"Signal with PnL should have closed status, got {status}"
    
    def test_signals_without_pnl(self, base_url):
        """Test sygnałów bez PnL (aktywne sygnały)."""
        response = requests.get(f'{base_url}/api/signals?limit=30')
        assert response.status_code == 200
        
        signals = response.json()
        signals_without_pnl = [s for s in signals if s.get('pnl') is None]
        
        # Sprawdź czy sygnały bez PnL mają odpowiedni status
        for signal in signals_without_pnl:
            status = signal['status']
            assert status in ['NEW', 'ENTRY_HIT'], \
                f"Signal without PnL should have active status, got {status}"
    
    def test_signals_api_limit_parameter(self, base_url):
        """Test parametru limit w API sygnałów."""
        # Test różnych limitów
        for limit in [1, 5, 10, 20]:
            response = requests.get(f'{base_url}/api/signals?limit={limit}')
            assert response.status_code == 200
            
            signals = response.json()
            assert len(signals) <= limit, f"Returned {len(signals)} signals, expected max {limit}"
    
    def test_signals_api_error_handling(self, base_url):
        """Test obsługi błędów w API sygnałów."""
        # Test nieprawidłowego limitu
        response = requests.get(f'{base_url}/api/signals?limit=invalid')
        # API powinno obsłużyć błąd gracefully
        assert response.status_code in [200, 400], "Should handle invalid limit parameter"
    
    @pytest.mark.slow
    def test_signals_api_performance(self, base_url):
        """Test wydajności API sygnałów."""
        import time
        
        start_time = time.time()
        response = requests.get(f'{base_url}/api/signals?limit=50')
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 5.0, "API response should be under 5 seconds"


def test_signals_api_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    try:
        response = requests.get('http://localhost:5000/api/signals?limit=10')
        if response.status_code == 200:
            signals = response.json()
            
            print("🔍 TEST API SYGNAŁÓW - FORMAT PnL")
            print("=" * 50)
            
            print(f"Pobrano {len(signals)} sygnałów")
            print()
            
            for i, signal in enumerate(signals[:5]):
                print(f"Sygnał {i+1}:")
                print(f"  ID: {signal.get('id')}")
                print(f"  Pair: {signal.get('pair')}")
                print(f"  Status: {signal.get('status')}")
                print(f"  PnL (raw): {repr(signal.get('pnl'))}")
                print(f"  PnL type: {type(signal.get('pnl'))}")
                
                # Sprawdź czy ma pnl_percent
                if 'pnl_percent' in signal:
                    print(f"  PnL percent: {signal.get('pnl_percent')}")
                
                print()
            
            # Sprawdź sygnały z PnL
            signals_with_pnl = [s for s in signals if s.get('pnl') is not None]
            print(f"Sygnały z PnL: {len(signals_with_pnl)}")
            
            if signals_with_pnl:
                print("Przykłady sygnałów z PnL:")
                for signal in signals_with_pnl[:3]:
                    pnl = signal.get('pnl')
                    print(f"  {signal['pair']}: PnL = {pnl} ({type(pnl).__name__})")
        
        else:
            print(f"Błąd API: {response.status_code}")
            
    except Exception as e:
        print(f"Błąd: {e}")


if __name__ == "__main__":
    test_signals_api_manual()
