#!/usr/bin/env python3
"""
Test bezpośrednio API dashboard.
Debug test for direct API inspection.
"""

import pytest
import requests
import json


class TestAPIDirectDebug:
    """Debug tests for direct API inspection."""

    def test_api_direct_signals(self, base_url):
        """Test bezpośrednio API sygnałów z debugowaniem."""
        response = requests.get(f'{base_url}/api/signals?limit=3')

        print(f"\n🔍 TEST BEZPOŚREDNIO API DASHBOARD")
        print("=" * 40)

        if response.status_code == 200:
            signals = response.json()

            print("Odpowiedź API:")
            for signal in signals:
                print(f"ID {signal['id']}: {signal['pair']} - Status: {signal['status']}")
                print(f"  PnL (raw): {repr(signal.get('pnl'))}")
                print(f"  PnL type: {type(signal.get('pnl'))}")
                if 'pnl_percent' in signal:
                    print(f"  PnL percent: {signal.get('pnl_percent')}")
                print()

            # Assertions for automated testing
            assert isinstance(signals, list)
            for signal in signals:
                assert 'id' in signal
                assert 'pair' in signal
                assert 'status' in signal
        else:
            print(f"Błąd API: {response.status_code}")
            print(response.text)
            pytest.fail(f"API returned {response.status_code}")

    def test_api_raw_response_inspection(self, base_url):
        """Inspekcja surowej odpowiedzi API."""
        response = requests.get(f'{base_url}/api/signals?limit=1')

        print(f"\n🔍 INSPEKCJA SUROWEJ ODPOWIEDZI API")
        print("=" * 40)

        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content-Type: {response.headers.get('content-type')}")
        print(f"Response Length: {len(response.text)}")
        print(f"Raw Response (first 500 chars): {response.text[:500]}")

        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON parsed successfully: {type(data)}")
                if isinstance(data, list) and data:
                    print(f"First item structure: {json.dumps(data[0], indent=2, default=str)}")
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                pytest.fail("Failed to decode JSON response")

    def test_api_error_responses(self, base_url):
        """Test odpowiedzi błędów API."""
        print(f"\n🔍 TEST ODPOWIEDZI BŁĘDÓW API")
        print("=" * 40)

        # Test nieistniejącego endpoint
        response = requests.get(f'{base_url}/api/nonexistent')
        print(f"Nonexistent endpoint: {response.status_code}")

        # Test nieprawidłowych parametrów
        response = requests.get(f'{base_url}/api/signals?limit=abc')
        print(f"Invalid limit parameter: {response.status_code}")

        # Test bardzo dużego limitu
        response = requests.get(f'{base_url}/api/signals?limit=99999')
        print(f"Large limit parameter: {response.status_code}")

    @pytest.mark.debug
    def test_api_detailed_signal_inspection(self, base_url):
        """Szczegółowa inspekcja struktury sygnałów."""
        response = requests.get(f'{base_url}/api/signals?limit=5')
        assert response.status_code == 200

        signals = response.json()

        print(f"\n🔍 SZCZEGÓŁOWA INSPEKCJA SYGNAŁÓW")
        print("=" * 40)

        for i, signal in enumerate(signals):
            print(f"\nSygnał {i+1}:")
            for key, value in signal.items():
                print(f"  {key}: {repr(value)} ({type(value).__name__})")

            # Sprawdź logikę PnL
            status = signal.get('status')
            pnl = signal.get('pnl')

            if status in ['TP_HIT', 'SL_HIT', 'EXPIRED']:
                if pnl is None:
                    print(f"  ⚠️  WARNING: Closed signal without PnL!")
            elif status in ['NEW', 'ENTRY_HIT']:
                if pnl is not None:
                    print(f"  ⚠️  WARNING: Active signal with PnL!")


def test_api_direct_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    print("🔍 TEST BEZPOŚREDNIO API DASHBOARD")
    print("=" * 40)

    try:
        # Test API signals
        response = requests.get('http://localhost:5000/api/signals?limit=3')
        if response.status_code == 200:
            signals = response.json()

            print("Odpowiedź API:")
            for signal in signals:
                print(f"ID {signal['id']}: {signal['pair']} - Status: {signal['status']}")
                print(f"  PnL (raw): {repr(signal.get('pnl'))}")
                print(f"  PnL type: {type(signal.get('pnl'))}")
                if 'pnl_percent' in signal:
                    print(f"  PnL percent: {signal.get('pnl_percent')}")
                print()
        else:
            print(f"Błąd API: {response.status_code}")
            print(response.text)

    except Exception as e:
        print(f"Błąd: {e}")


def test_new_api_manual():
    """Test nowego API z poprawkami (kompatybilność wsteczna)."""
    print("🔍 TEST NOWEGO API Z POPRAWKAMI")
    print("=" * 40)

    try:
        # Test API signals z małą liczbą
        response = requests.get('http://localhost:5000/api/signals?limit=5')
        if response.status_code == 200:
            signals = response.json()

            print(f"Pobrano {len(signals)} sygnałów")
            print()

            for i, signal in enumerate(signals):
                print(f"Sygnał {i+1}:")
                print(f"  ID: {signal['id']}")
                print(f"  Pair: {signal['pair']}")
                print(f"  Status: {signal['status']}")
                print(f"  PnL: {repr(signal.get('pnl'))} ({type(signal.get('pnl')).__name__})")
                print(f"  PnL percent: {signal.get('pnl_percent')}")

                # Sprawdź czy to sygnał z rzeczywistym PnL
                if signal['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']:
                    print(f"  ⚠️  To powinien być sygnał z PnL!")
                else:
                    print(f"  ✅ To sygnał bez PnL (status: {signal['status']})")
                print()

            # Sprawdź sygnały z rzeczywistym PnL
            real_pnl_signals = [s for s in signals if s.get('pnl') is not None and s['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']]
            print(f"Sygnały z rzeczywistym PnL: {len(real_pnl_signals)}")

            # Sprawdź sygnały bez PnL
            no_pnl_signals = [s for s in signals if s.get('pnl') is None and s['status'] in ['NEW', 'ENTRY_HIT']]
            print(f"Sygnały bez PnL (poprawnie): {len(no_pnl_signals)}")

        else:
            print(f"Błąd API: {response.status_code}")
            print(response.text)

    except Exception as e:
        print(f"Błąd: {e}")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "new":
        test_new_api_manual()
    else:
        test_api_direct_manual()
