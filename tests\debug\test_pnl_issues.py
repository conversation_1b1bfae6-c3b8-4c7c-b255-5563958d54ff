#!/usr/bin/env python3
"""
Test sygnałów z rzeczywistym PnL i zamkniętych sygnałów.
Debug tests for PnL calculation and signal status issues.
"""

import pytest
import requests
from typing import List, Dict, Any


class TestPnLIssuesDebug:
    """Debug tests for PnL calculation and signal status issues."""
    
    def test_real_pnl_signals(self, base_url):
        """Test sygnałów z rzeczywistym PnL."""
        response = requests.get(f'{base_url}/api/signals?limit=30')
        assert response.status_code == 200
        
        signals = response.json()
        
        print(f"\n🔍 TEST SYGNAŁÓW Z RZECZYWISTYM PnL")
        print("=" * 40)
        print(f"Pobrano {len(signals)} sygnałów")
        print()
        
        # Znajdź sygnały z PnL
        signals_with_pnl = [s for s in signals if s.get('pnl') is not None]
        print(f"Sygnały z PnL: {len(signals_with_pnl)}")
        
        if signals_with_pnl:
            print("\nSygnały z rzeczywistym PnL:")
            for signal in signals_with_pnl:
                pnl = signal.get('pnl')
                pnl_percent = signal.get('pnl_percent')
                print(f"ID {signal['id']}: {signal['pair']} - {signal['status']}")
                print(f"  PnL: {pnl} ({type(pnl).__name__})")
                print(f"  PnL %: {pnl_percent}")
                print()
                
                # Assertions for automated testing
                assert isinstance(pnl, (int, float)), f"PnL should be numeric, got {type(pnl)}"
                assert signal['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED'], \
                    f"Signal with PnL should have closed status, got {signal['status']}"
        else:
            print("Brak sygnałów z PnL")
        
        # Sprawdź statusy
        print("Rozkład statusów:")
        status_counts = {}
        for signal in signals:
            status = signal['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in status_counts.items():
            print(f"  {status}: {count}")
        
        return signals_with_pnl
    
    def test_closed_signals(self, base_url):
        """Test zamkniętych sygnałów z PnL."""
        response = requests.get(f'{base_url}/api/signals?limit=30')
        assert response.status_code == 200
        
        signals = response.json()
        
        print(f"\n🔍 TEST ZAMKNIĘTYCH SYGNAŁÓW Z PnL")
        print("=" * 40)
        
        # Znajdź zamknięte sygnały
        closed_signals = [s for s in signals if s['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']]
        print(f"Zamknięte sygnały: {len(closed_signals)}")
        print()
        
        if closed_signals:
            print("Przykłady zamkniętych sygnałów:")
            for signal in closed_signals[:5]:
                pnl = signal.get('pnl')
                pnl_percent = signal.get('pnl_percent')
                print(f"ID {signal['id']}: {signal['pair']} - {signal['status']}")
                print(f"  PnL: {pnl} ({type(pnl).__name__})")
                print(f"  PnL %: {pnl_percent}")
                print()
                
                # Check for inconsistencies
                if pnl is None:
                    print(f"  ⚠️  WARNING: Closed signal without PnL!")
        else:
            print("Brak zamkniętych sygnałów w próbce")
        
        return closed_signals
    
    def test_pnl_consistency(self, base_url):
        """Test spójności danych PnL."""
        response = requests.get(f'{base_url}/api/signals?limit=50')
        assert response.status_code == 200
        
        signals = response.json()
        
        print(f"\n🔍 TEST SPÓJNOŚCI DANYCH PnL")
        print("=" * 40)
        
        inconsistencies = []
        
        for signal in signals:
            status = signal['status']
            pnl = signal.get('pnl')
            
            # Sprawdź niespójności
            if status in ['TP_HIT', 'SL_HIT', 'EXPIRED'] and pnl is None:
                inconsistencies.append({
                    'id': signal['id'],
                    'pair': signal['pair'],
                    'status': status,
                    'issue': 'Closed signal without PnL'
                })
            elif status in ['NEW', 'ENTRY_HIT'] and pnl is not None:
                inconsistencies.append({
                    'id': signal['id'],
                    'pair': signal['pair'],
                    'status': status,
                    'issue': 'Active signal with PnL'
                })
        
        print(f"Znalezione niespójności: {len(inconsistencies)}")
        
        if inconsistencies:
            print("\nNiespójności:")
            for issue in inconsistencies[:10]:  # Pokaż pierwsze 10
                print(f"  ID {issue['id']}: {issue['pair']} - {issue['status']} - {issue['issue']}")
        
        return inconsistencies
    
    @pytest.mark.debug
    def test_pnl_calculation_debug(self, base_url):
        """Debug kalkulacji PnL."""
        response = requests.get(f'{base_url}/api/signals?limit=10')
        assert response.status_code == 200
        
        signals = response.json()
        
        print(f"\n🔍 DEBUG KALKULACJI PnL")
        print("=" * 40)
        
        for signal in signals:
            if signal.get('pnl') is not None:
                print(f"\nSygnał ID {signal['id']}:")
                print(f"  Pair: {signal['pair']}")
                print(f"  Direction: {signal.get('direction', 'N/A')}")
                print(f"  Entry: {signal.get('entry_price', 'N/A')}")
                print(f"  TP: {signal.get('tp_price', 'N/A')}")
                print(f"  SL: {signal.get('sl_price', 'N/A')}")
                print(f"  Status: {signal['status']}")
                print(f"  PnL: {signal['pnl']}")
                print(f"  PnL %: {signal.get('pnl_percent', 'N/A')}")
                
                # Sprawdź czy kalkulacja ma sens
                entry = signal.get('entry_price')
                tp = signal.get('tp_price')
                sl = signal.get('sl_price')
                direction = signal.get('direction')
                status = signal['status']
                
                if all(x is not None for x in [entry, tp, sl, direction]):
                    if status == 'TP_HIT':
                        expected_positive = (direction == 'LONG' and tp > entry) or (direction == 'SHORT' and tp < entry)
                        actual_positive = signal['pnl'] > 0
                        if expected_positive != actual_positive:
                            print(f"  ⚠️  PnL sign mismatch for TP_HIT!")
                    elif status == 'SL_HIT':
                        expected_negative = (direction == 'LONG' and sl < entry) or (direction == 'SHORT' and sl > entry)
                        actual_negative = signal['pnl'] < 0
                        if expected_negative != actual_negative:
                            print(f"  ⚠️  PnL sign mismatch for SL_HIT!")


def test_real_pnl_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    print("🔍 TEST SYGNAŁÓW Z RZECZYWISTYM PnL")
    print("=" * 40)
    
    try:
        response = requests.get('http://localhost:5000/api/signals?limit=30')
        if response.status_code == 200:
            signals = response.json()
            
            print(f"Pobrano {len(signals)} sygnałów")
            print()
            
            # Znajdź sygnały z PnL
            signals_with_pnl = [s for s in signals if s.get('pnl') is not None]
            print(f"Sygnały z PnL: {len(signals_with_pnl)}")
            
            if signals_with_pnl:
                print("\nSygnały z rzeczywistym PnL:")
                for signal in signals_with_pnl:
                    pnl = signal.get('pnl')
                    pnl_percent = signal.get('pnl_percent')
                    print(f"ID {signal['id']}: {signal['pair']} - {signal['status']}")
                    print(f"  PnL: {pnl} ({type(pnl).__name__})")
                    print(f"  PnL %: {pnl_percent}")
                    print()
            else:
                print("Brak sygnałów z PnL")
                
            # Sprawdź statusy
            print("Rozkład statusów:")
            status_counts = {}
            for signal in signals:
                status = signal['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                print(f"  {status}: {count}")
                
        else:
            print(f"Błąd API: {response.status_code}")
            
    except Exception as e:
        print(f"Błąd: {e}")


def test_closed_signals_manual():
    """Funkcja do manualnego testowania (kompatybilność wsteczna)."""
    print("🔍 TEST ZAMKNIĘTYCH SYGNAŁÓW Z PnL")
    print("=" * 40)
    
    try:
        response = requests.get('http://localhost:5000/api/signals?limit=30')
        if response.status_code == 200:
            signals = response.json()
            
            # Znajdź zamknięte sygnały
            closed_signals = [s for s in signals if s['status'] in ['TP_HIT', 'SL_HIT', 'EXPIRED']]
            print(f"Zamknięte sygnały: {len(closed_signals)}")
            print()
            
            if closed_signals:
                print("Przykłady zamkniętych sygnałów:")
                for signal in closed_signals[:5]:
                    pnl = signal.get('pnl')
                    pnl_percent = signal.get('pnl_percent')
                    print(f"ID {signal['id']}: {signal['pair']} - {signal['status']}")
                    print(f"  PnL: {pnl} ({type(pnl).__name__})")
                    print(f"  PnL %: {pnl_percent}")
                    print()
            else:
                print("Brak zamkniętych sygnałów w próbce")
                
        else:
            print(f"Błąd API: {response.status_code}")
            
    except Exception as e:
        print(f"Błąd: {e}")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "closed":
        test_closed_signals_manual()
    else:
        test_real_pnl_manual()
