<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Logi Administratora - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-accent: #3498db;
            --admin-success: #27ae60;
            --admin-warning: #f39c12;
            --admin-danger: #e74c3c;
            --admin-dark: #1a252f;
            --logs-color: #e67e22;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-navbar {
            background: var(--admin-dark) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .admin-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
        }

        .logs-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 20px;
        }

        .logs-card .card-header {
            background: linear-gradient(135deg, var(--logs-color) 0%, #f39c12 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--admin-accent) 0%, #5dade2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            color: white;
        }

        .btn-logs {
            background: linear-gradient(135deg, var(--logs-color) 0%, #f39c12 100%);
        }

        .log-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid var(--logs-color);
            transition: all 0.3s ease;
        }

        .log-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .log-action {
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-right: 8px;
        }

        .action-CREATE {
            background: var(--admin-success);
            color: white;
        }

        .action-UPDATE {
            background: var(--admin-warning);
            color: white;
        }

        .action-DELETE {
            background: var(--admin-danger);
            color: white;
        }

        .log-entity {
            font-weight: 500;
            color: var(--admin-primary);
        }

        .log-timestamp {
            color: #666;
            font-size: 0.9rem;
        }

        .log-ip {
            color: #888;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }

        .log-details {
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid #dee2e6;
        }

        .log-values {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            background: #f1f3f4;
            padding: 8px;
            border-radius: 4px;
            margin-top: 5px;
            max-height: 100px;
            overflow-y: auto;
        }

        .filters-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .filter-group {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--logs-color);
            box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.25);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .stats-row {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--logs-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="/">
                <i class="fas fa-list-alt"></i>
                Logi Administratora
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-light" href="/">
                    <i class="fas fa-arrow-left"></i>
                    Powrót do Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="admin-content fade-in">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0">
                    <i class="fas fa-list-alt text-primary"></i>
                    Logi Administratora
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-admin" onclick="loadLogs()">
                        <i class="fas fa-sync-alt"></i>
                        Odśwież
                    </button>
                    <button class="btn btn-logs" onclick="exportLogs()">
                        <i class="fas fa-download"></i>
                        Eksportuj
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-row">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="totalLogs">0</div>
                            <div class="stat-label">Wszystkich logów</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="todayLogs">0</div>
                            <div class="stat-label">Dzisiaj</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="createActions">0</div>
                            <div class="stat-label">Utworzono</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number" id="deleteActions">0</div>
                            <div class="stat-label">Usunięto</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <h5 class="mb-3">
                    <i class="fas fa-filter"></i>
                    Filtry
                </h5>
                <div class="filter-group">
                    <div>
                        <label for="entityTypeFilter" class="form-label">Typ encji</label>
                        <select class="form-select" id="entityTypeFilter">
                            <option value="">Wszystkie</option>
                            <option value="strategy">Strategie</option>
                            <option value="channel">Kanały</option>
                            <option value="config">Konfiguracja</option>
                            <option value="bot_whitelist">Boty</option>
                        </select>
                    </div>
                    <div>
                        <label for="actionFilter" class="form-label">Akcja</label>
                        <select class="form-select" id="actionFilter">
                            <option value="">Wszystkie</option>
                            <option value="CREATE">Utworzenie</option>
                            <option value="UPDATE">Aktualizacja</option>
                            <option value="DELETE">Usunięcie</option>
                        </select>
                    </div>
                    <div>
                        <label for="limitFilter" class="form-label">Limit</label>
                        <select class="form-select" id="limitFilter">
                            <option value="50">50</option>
                            <option value="100" selected>100</option>
                            <option value="200">200</option>
                            <option value="500">500</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-logs d-block" onclick="applyFilters()">
                            <i class="fas fa-search"></i>
                            Filtruj
                        </button>
                    </div>
                </div>
            </div>

            <!-- Logs List -->
            <div class="logs-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i>
                        Historia akcji
                    </h5>
                </div>
                <div class="card-body">
                    <div id="logsContainer">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Ładowanie...</span>
                            </div>
                            <p class="mt-2">Ładowanie logów...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let logs = [];
        let filteredLogs = [];

        // Inicjalizacja
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
        });

        // Ładowanie logów
        async function loadLogs() {
            try {
                const limit = document.getElementById('limitFilter').value;
                const entityType = document.getElementById('entityTypeFilter').value;
                
                let url = `/api/logs?limit=${limit}`;
                if (entityType) {
                    url += `&entity_type=${entityType}`;
                }
                
                const response = await fetch(url);
                if (!response.ok) throw new Error('Błąd ładowania logów');
                
                logs = await response.json();
                filteredLogs = logs;
                renderLogs();
                updateStats();
            } catch (error) {
                console.error('Error loading logs:', error);
                showError('Błąd ładowania logów: ' + error.message);
            }
        }

        // Zastosuj filtry
        function applyFilters() {
            const entityType = document.getElementById('entityTypeFilter').value;
            const action = document.getElementById('actionFilter').value;
            
            filteredLogs = logs.filter(log => {
                if (entityType && log.entity_type !== entityType) return false;
                if (action && log.action !== action) return false;
                return true;
            });
            
            renderLogs();
            updateStats();
        }

        // Renderowanie logów
        function renderLogs() {
            const container = document.getElementById('logsContainer');
            
            if (filteredLogs.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                        <h5>Brak logów</h5>
                        <p class="text-muted">Nie znaleziono logów spełniających kryteria filtrowania.</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredLogs.map(log => `
                <div class="log-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="mb-2">
                                <span class="log-action action-${log.action}">${log.action}</span>
                                <span class="log-entity">${log.entity_type}</span>
                                ${log.entity_id ? `<span class="text-muted">(ID: ${log.entity_id})</span>` : ''}
                            </div>
                            
                            <div class="log-timestamp">
                                <i class="fas fa-clock"></i>
                                ${formatTimestamp(log.timestamp)}
                            </div>
                            
                            ${log.user_ip ? `
                                <div class="log-ip">
                                    <i class="fas fa-globe"></i>
                                    IP: ${log.user_ip}
                                </div>
                            ` : ''}
                        </div>
                        
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleLogDetails(${log.id})">
                            <i class="fas fa-eye"></i>
                            Szczegóły
                        </button>
                    </div>
                    
                    <div id="details-${log.id}" class="log-details" style="display: none;">
                        ${log.old_values ? `
                            <div class="mb-2">
                                <strong>Stare wartości:</strong>
                                <div class="log-values">${formatLogValues(log.old_values)}</div>
                            </div>
                        ` : ''}
                        
                        ${log.new_values ? `
                            <div class="mb-2">
                                <strong>Nowe wartości:</strong>
                                <div class="log-values">${formatLogValues(log.new_values)}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        // Przełącz szczegóły logu
        function toggleLogDetails(logId) {
            const details = document.getElementById(`details-${logId}`);
            if (details.style.display === 'none') {
                details.style.display = 'block';
            } else {
                details.style.display = 'none';
            }
        }

        // Formatuj timestamp
        function formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('pl-PL', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // Formatuj wartości logów
        function formatLogValues(values) {
            try {
                if (typeof values === 'string') {
                    const parsed = JSON.parse(values);
                    return JSON.stringify(parsed, null, 2);
                }
                return JSON.stringify(values, null, 2);
            } catch (e) {
                return values;
            }
        }

        // Aktualizuj statystyki
        function updateStats() {
            const today = new Date().toDateString();
            
            const totalLogs = filteredLogs.length;
            const todayLogs = filteredLogs.filter(log => 
                new Date(log.timestamp).toDateString() === today
            ).length;
            const createActions = filteredLogs.filter(log => log.action === 'CREATE').length;
            const deleteActions = filteredLogs.filter(log => log.action === 'DELETE').length;
            
            document.getElementById('totalLogs').textContent = totalLogs;
            document.getElementById('todayLogs').textContent = todayLogs;
            document.getElementById('createActions').textContent = createActions;
            document.getElementById('deleteActions').textContent = deleteActions;
        }

        // Eksportuj logi
        function exportLogs() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "Timestamp,Action,Entity Type,Entity ID,User IP,Old Values,New Values\n"
                + filteredLogs.map(log => [
                    log.timestamp,
                    log.action,
                    log.entity_type,
                    log.entity_id || '',
                    log.user_ip || '',
                    (log.old_values || '').replace(/"/g, '""'),
                    (log.new_values || '').replace(/"/g, '""')
                ].map(field => `"${field}"`).join(",")).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `admin_logs_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Funkcje pomocnicze
        function showError(message) {
            console.error('Error:', message);
            alert(message); // Tymczasowe rozwiązanie
        }
    </script>
</body>
</html>
